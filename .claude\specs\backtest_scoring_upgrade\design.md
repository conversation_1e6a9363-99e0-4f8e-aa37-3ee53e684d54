# 回测评分体系升级设计文档

## 概述

基于需求分析，本设计文档详细描述了如何将现有的 `backtestv6.py` 回测脚本从简单的资金流排序工具升级为能够动态理解市场结构、识别多信号共振的强大分析引擎。升级后的系统将引入多维度评分体系、时间敏感权重、瞬时异动数据整合以及分级信号输出机制。

## 架构

### 现有架构分析

**当前系统结构**： 
```
backtestv6.py (主回测引擎)
可以参考doc\fund_data_analyzer.txt文件怎么读取所有不同的文件，因为相同的文件也可能存在不同的命名，例如个股资金流可能有多个文件命名
├── get_all_capital_flow_east_5_test.py (核心评分逻辑)
├── data/YYYY-MM-DD/ (按分钟存储的市场数据)
│   ├── fund_flow_rank_*.csv (个股资金流排名)
│   ├── sector_fund_flow_rank_*.csv (板块资金流)
│   ├── acceleration_signals_*.csv (资金加速度信号)
│   ├── big_deal_*.csv (大单交易数据)
│   └── sector_summary_*.csv (板块龙头股数据)
└── backtest/YYYYMMDD/ (输出日志和报告)
```

**现有评分机制**：
- 基于 `BUY_CONDITIONS_SCORE` 字典的简单加分制
- 主要依赖板块身份加成和资金流排名
- 缺乏时间维度敏感性和瞬时信号整合

### 新架构设计

**升级后的架构**将保持现有文件结构不变，但重构核心评分引擎：

```
backtestv6.py (升级后的主回测引擎)
├── 新增：REPORT_DRIVEN_SCORE (多维度评分体系)
├── 新增：SIGNAL_THRESHOLDS (分级信号阈值)
├── 新增：时间敏感评分逻辑
├── 新增：瞬时信号追踪器
└── 增强：信号输出和报告格式
```

## 组件和接口

### 1. 多维度评分体系 (REPORT_DRIVEN_SCORE)

**核心评分字典设计**：
```python
REPORT_DRIVEN_SCORE = {
    # A+级核心信号 (决定性因素) - 10-8分
    'MAINLINE_LEADER_EARLY': 10,    # 主线龙头+早盘
    'ACCELERATION_MAINLINE': 8,     # 主线+加速度
    'BIG_BUY_EARLY_MAINLINE': 8,    # 主线+早盘大买单
    
    # A级核心信号 (强力加分项) - 5-3分
    'MAINLINE_STOCK': 5,            # 主线身份
    'SECTOR_LEADER_TOP1': 5,        # 板块龙头
    'FUND_FLOW_TOP_10': 4,          # 资金流Top10
    'BIG_BUY_CONTINUOUS': 4,        # 连续大买单
    'SECTOR_PERSISTENCE_HIGH': 3,   # 板块持续性
    
    # B级辅助信号 (重要观察点) - 2分
    'STRONG_SECTOR_STOCK': 2,       # 强势板块身份
    'FUND_FLOW_TOP_20': 2,          # 资金流Top20
    'BIG_BUY_SINGLE': 2,            # 单次大买单
    'RANK_JUMP': 2,                 # 排名跃升
    'PRICE_BREAKOUT_STRONG': 2,     # 强势价格突破
    
    # C级观察信号 (环境与确认) - 1分
    'SECTOR_NET_INFLOW': 1,         # 板块资金流入
    'AFTERNOON_BREAKOUT': 1,        # 午后异动
    'CHIP_CONCENTRATION': 1         # 筹码集中
}
```

**设计原理**：
- **信号共振机制**：多个中等信号可累积成强信号
- **权重递减设计**：决定性因素权重最高，环境因素权重最低
- **量化精确性**：每个信号都有明确的触发条件和计算逻辑

### 2. 信号分级阈值系统

**分级标准**：
```python
SIGNAL_THRESHOLDS = {
    'A+': 15,  # 大师级信号：多项核心信号共振，成功率极高
    'A': 12,   # 专业级信号：具备明确的龙头相，成功率高  
    'B': 8     # 进阶级信号：值得重点关注的潜力股
}
```

**分级逻辑**：
- A+级：至少需要1个A+级核心信号 + 其他信号组合
- A级：需要主线身份 + 板块龙头地位或强资金信号
- B级：强势板块 + 多个辅助信号的组合

### 3. 时间敏感评分引擎

**时间窗口定义**：
```python
# 早盘黄金时间 (9:15-10:00)
is_early_morning = current_sim_time < time(10, 0)

# 午后异动时间 (13:30-15:00)
is_afternoon = current_sim_time >= time(13, 30)
```

**时间敏感逻辑**：
- **早盘权重放大**：主线龙头在早盘时间获得最高评分
- **午后首次异动**：识别午后首次发力的股票给予特殊标记
- **连续信号追踪**：3分钟内连续大买单的识别和评分

### 4. 瞬时信号追踪系统

**大买单追踪器设计**：
```python
stock_big_buy_tracker = {}  # 格式: {'代码': [时间1, 时间2, ...]}
```

**瞬时信号处理流程**：
1. **资金加速度信号**：从 `acceleration_signals_*.csv` 文件读取
2. **大买单信号**：从 `big_deal_*.csv` 文件读取，按买卖方向分类
3. **排名跃升信号**：基于历史排名数据计算排名变化
4. **连续性判断**：3分钟内多次大买单的累积效应

### 5. 板块强度量化模块

**板块地位识别**：
```python
# 板块龙头识别 (资金排名第1)
is_sector_leader_top1 = stock_code == sector_leaders[0]

# 板块持续性评估
sector_persistence = SECTOR_STRENGTH_TRACKER_BT.get(sector_name, 0)
```

**板块联动分析**：
- **主线vs支线区分**：基于 `MAINLINE_SECTOR_LIST_BT` 和 `STRONG_SECTORS_LIST_BT`
- **龙头地位评估**：在板块内的资金排名位置
- **持续性强度**：板块连续强势的时间长度

## 数据模型

### 输入数据结构

**主要数据源**：
```
1. fund_flow_rank_*.csv - 个股资金流排名
   字段：代码, 名称, 最新价, 今日涨跌幅, 今日主力净流入-净额, rank

2. acceleration_signals_*.csv - 资金加速度信号  
   字段：Time, Stock_Code, Stock_Name, Acceleration_Amount

3. big_deal_*.csv - 大单交易数据
   字段：成交时间, 股票代码, 成交额, 大单性质

4. sector_summary_*.csv - 板块龙头数据
   字段：代码, 名称, 板块内排名, 资金流入
```

### 输出数据结构

**增强的信号记录结构**：
```python
signal_info = {
    'Time': f"{date_str} {first_triggered_time}",
    'Stock_Code': stock_code,
    'Stock_Name': stock_name,
    'Price': row.get('最新价', 0),
    'Change_Percent': change_percent,
    'Main_Flow': main_net_abs,
    'Score': score,                    # 新增：总评分
    'Score_Reasons': ' + '.join(reasons),  # 新增：评分明细
    'Signal_Level': signal_level,      # 新增：信号级别 (A+/A/B)
    'Mainline_Sectors': mainline_sectors,
    'Potential_Sectors': potential_sectors
}
```

### 状态追踪数据

**新增状态变量**：
```python
# 大买单追踪
stock_big_buy_tracker = {}  # {'代码': [时间列表]}

# 已处理信号集合
processed_buy_signals = set()  # 避免重复信号

# 瞬时信号缓存
acceleration_data_by_time = {}  # 按时间索引的加速度数据
big_deal_data_by_time = {}      # 按时间索引的大单数据
rank_change_data_by_time = {}   # 按时间索引的排名变化数据
```

## 错误处理

### 数据完整性保障

**文件读取容错**：
```python
try:
    current_rank_df = pd.read_csv(file_path, encoding='utf-8-sig')
    current_rank_df['代码'] = current_rank_df['代码'].astype(str).str.zfill(6)
except Exception as e:
    logging.error(f"读取文件 {file_path} 失败: {e}")
    continue  # 跳过该时间点，继续处理
```

**数据格式标准化**：
- 股票代码统一为6位字符串格式
- 数值字段使用 `convert_to_float()` 函数安全转换
- 缺失字段使用合理默认值填充

### 时间序列一致性

**时间窗口验证**：
- 确保只处理交易时间内的数据 (9:15-15:00)
- 跳过非交易时间和异常时间点
- 保持时间序列的连续性和完整性

**未来函数预防**：
- 严格按时间顺序处理数据
- 禁止使用当前时间点之后的数据
- 所有决策只基于当前及历史数据

### 评分系统稳定性

**评分边界控制**：
- 设置评分上限，防止异常高分
- 空值和异常数据的安全处理
- 评分逻辑的幂等性保证

## 测试策略

### 单元测试覆盖

**核心功能模块测试**：
1. **评分系统测试**：验证各评分项的计算逻辑
2. **时间敏感性测试**：验证早盘/午后时间窗口识别
3. **信号分级测试**：验证A+/A/B级信号阈值逻辑
4. **数据追踪测试**：验证大买单连续性识别

### 集成测试方案

**历史数据回测验证**：
1. **基准对比测试**：与原版本输出结果对比
2. **信号质量评估**：统计各级别信号的后续表现
3. **性能压力测试**：大数据量下的处理性能
4. **边界情况测试**：无信号日、极端市场条件

### 数据一致性验证

**输出格式检查**：
- 新旧报告格式的兼容性验证
- 日志输出的完整性检查
- 文件结构和命名约定的一致性

**结果可重现性**：
- 相同输入数据的一致输出
- 随机性因素的控制和隔离
- 版本间结果差异的可追溯性

## 关键设计决策和理由

### 1. 保持向后兼容

**决策**：升级采用增量修改而非重写
**理由**：
- 保护现有投资和使用习惯
- 降低升级风险和测试成本
- 维持现有数据流和文件格式

### 2. 分级信号体系

**决策**：引入A+/A/B三级信号分类
**理由**：
- 满足不同风险偏好的投资需求
- 提供信号质量的量化评估
- 便于批量处理和优先级排序

### 3. 时间敏感权重设计

**决策**：早盘信号获得更高权重
**理由**：
- 符合市场开盘后的流动性特征
- 早盘异动通常预示全天走势
- 与实际交易经验和策略一致

### 4. 瞬时信号整合策略

**决策**：将异动信号从报告功能提升为评分核心
**理由**：
- 提高信号的实时性和敏感度
- 捕捉市场微观结构变化
- 增强多因子信号共振效应

### 5. 渐进式实施方案

**决策**：分阶段实施升级，先核心后外围
**理由**：
- 降低系统复杂度和实施风险
- 便于问题定位和性能优化
- 支持快速迭代和用户反馈

这个设计确保了系统的可扩展性、可维护性和高性能，同时满足了所有功能需求并保持了与现有系统的兼容性。