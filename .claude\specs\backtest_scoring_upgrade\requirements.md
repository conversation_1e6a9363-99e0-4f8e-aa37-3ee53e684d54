# 回测评分体系升级需求文档

## 功能概述

升级现有的股票回测脚本 `backtestv6.py`，从简单的资金流排序工具转变为能够动态理解市场结构、识别多信号共振的强大分析引擎。新系统将引入多维度评分体系、时间敏感权重、瞬时异动数据整合以及分级信号输出机制。

## 功能需求

### 1. 多维度评分体系重构

**用户故事**: 作为量化交易分析师，我希望回测系统能够使用多维度评分体系替代原有简单加分制，以便更准确地识别具有多重因素共振的潜力股票。

**验收标准**:
1.1. 系统应当定义A+级核心信号评分项（决定性因素），包括主线龙头早盘信号、主线加速度信号、主线早盘大买单信号
1.2. 系统应当定义A级核心信号评分项（强力加分项），包括主线身份、板块龙头、资金流排名、连续大买单、板块持续性
1.3. 系统应当定义B级辅助信号评分项（重要观察点），包括强势板块身份、资金流排名、单次大买单、排名跃升、价格突破
1.4. 系统应当定义C级观察信号评分项（环境与确认），包括板块资金流入、午后异动、筹码集中
1.5. 系统应当为每个评分项设置明确的分值权重

### 2. 时间维度敏感性增强

**用户故事**: 作为交易策略师，我希望系统能够对不同时间段出现的信号给予不同权重，特别是识别开盘半小时内的黄金时间信号，以便提高信号的时效性和准确性。

**验收标准**:
2.1. 系统应当识别早盘时间窗口（10:00前）并对此时段内的信号给予更高权重
2.2. 系统应当识别午后时间窗口（13:30后）并对首次异动给予特殊标记
2.3. 系统应当对主线龙头在早盘时间的表现给予最高权重评分
2.4. 系统应当对早盘时间内出现的千万级以上大买单在主线板块中给予特殊加分

### 3. 瞬时异动数据整合

**用户故事**: 作为市场分析师，我希望系统能够将资金加速度、大单买入、排名跃升等瞬时数据从单纯的报告功能提升为核心评分项，以便更好地捕捉市场的实时动态。

**验收标准**:
3.1. 系统应当追踪并记录个股的连续大买单行为（3分钟内2次或以上）
3.2. 系统应当识别并评分资金加速度信号，特别是主线板块中的加速度信号
3.3. 系统应当识别并评分排名跃升异动信号
3.4. 系统应当根据大买单金额（500万以上、1000万以上）进行分级评分
3.5. 系统应当将瞬时异动数据与板块归属相结合进行综合评分

### 4. 板块强度与联动量化

**用户故事**: 作为板块轮动分析师，我希望系统不仅能看板块是否强势，还要看个股在板块中的地位以及板块自身的持续性，以便更准确地判断个股的投资价值。

**验收标准**:
4.1. 系统应当识别个股在强势板块中的排名地位（龙头/跟风）
4.2. 系统应当评估板块的持续性强度（持续强势时间长度）
4.3. 系统应当区分主线板块和支线板块的不同权重
4.4. 系统应当计算个股与所属板块的联动强度
4.5. 系统应当对板块龙头（资金排名第1）给予特殊高分评价

### 5. 分级信号输出机制

**用户故事**: 作为投资决策者，我希望系统能够根据最终得分将信号分为A+级、A级、B级等不同等级，以便快速识别信号质量并做出相应的投资决策。

**验收标准**:
5.1. 系统应当定义A+级信号阈值（15分以上），代表大师级/A+级信号，多项核心信号共振，成功率极高
5.2. 系统应当定义A级信号阈值（12分以上），代表专业级/A级信号，具备明确的龙头相，成功率高
5.3. 系统应当定义B级信号阈值（8分以上），代表进阶级/B级信号，值得重点关注的潜力股
5.4. 系统应当在实时输出中标明信号级别前缀（如【A+级信号】）
5.5. 系统应当在最终报告中按信号级别和时间进行双重排序

### 6. 评分详情透明化

**用户故事**: 作为量化研究员，我希望系统能够详细显示每个信号的评分构成和原因，以便理解和验证评分逻辑的合理性。

**验收标准**:
6.1. 系统应当记录每个触发信号的详细评分构成
6.2. 系统应当显示每个评分项的具体分值和原因
6.3. 系统应当在实时输出中显示评分明细
6.4. 系统应当在最终报告中包含完整的评分原因说明
6.5. 系统应当支持评分逻辑的可追溯性和可审计性

### 7. 兼容性与稳定性保证

**用户故事**: 作为系统维护者，我希望升级后的系统能够保持与现有数据格式和输出格式的兼容性，确保系统稳定运行。

**验收标准**:
7.1. 系统应当保持与现有CSV数据文件格式的完全兼容性
7.2. 系统应当保持与现有日志输出格式的基本兼容性
7.3. 系统应当在升级后保持原有的错误处理机制
7.4. 系统应当在无信号触发时保持原有的处理逻辑
7.5. 系统应当保持与现有配置参数的兼容性
7.6. 系统应该完全避免未来函数