# 回测评分体系升级实施计划

## 实施概述

本实施计划将现有的 `backtestv6.py` 回测脚本升级为多维度信号分析引擎，按照测试驱动开发的方式，分阶段实施核心功能。每个任务都专注于具体的代码实现，确保增量进展和可测试性。

## 实施任务清单

### 1. 核心评分体系重构

#### 1.1 创建新的评分配置模块
- [ ] 在 `backtestv6.py` 中添加 `REPORT_DRIVEN_SCORE` 评分字典配置
- [ ] 添加 `SIGNAL_THRESHOLDS` 信号分级阈值配置  
- [ ] 添加时间窗口定义常量 (早盘、午后时间)
- [ ] 创建大买单追踪器数据结构 `stock_big_buy_tracker`
- 参考需求：1.1-1.5 (多维度评分体系重构)
- 参考设计：多维度评分体系 (REPORT_DRIVEN_SCORE)

#### 1.2 实现评分计算核心逻辑
- [ ] 编写板块身份识别函数 (主线vs强势板块判断)
- [ ] 实现板块龙头地位计算逻辑 (资金排名第1识别)
- [ ] 编写资金流排名评分函数 (Top10/Top20判断)
- [ ] 实现价格突破强度评分逻辑 (>7%, >5%, >3%分级)
- 参考需求：1.1-1.5, 4.1-4.5 (板块强度与联动量化)
- 参考设计：组件和接口 - 多维度评分体系

### 2. 时间敏感评分引擎

#### 2.1 实现时间窗口识别逻辑
- [ ] 编写早盘时间检测函数 `is_early_morning` (9:15-10:00)
- [ ] 编写午后时间检测函数 `is_afternoon` (13:30-15:00)  
- [ ] 实现时间敏感评分权重调整逻辑
- [ ] 添加午后首次异动检测机制
- 参考需求：2.1-2.4 (时间维度敏感性增强)
- 参考设计：时间敏感评分引擎

#### 2.2 整合时间权重到评分系统
- [ ] 修改主线龙头评分，在早盘时间给予最高权重 (10分)
- [ ] 实现早盘千万级大买单的特殊评分逻辑 (8分)
- [ ] 添加午后异动的评分逻辑 (1分)
- [ ] 测试时间敏感评分的正确性
- 参考需求：2.1-2.4, 5.1-5.5 (分级信号输出机制)
- 参考设计：时间敏感评分引擎

### 3. 瞬时异动信号整合

#### 3.1 实现数据文件读取增强
- [ ] 参考 `doc\fund_data_analyzer.txt` 完善文件类型识别
- [ ] 增强 `acceleration_signals_*.csv` 文件读取逻辑
- [ ] 增强 `big_deal_*.csv` 文件读取和分类逻辑 (买盘vs卖盘)
- [ ] 实现排名变化数据的收集和处理
- 参考需求：3.1-3.5 (瞬时异动数据整合)
- 参考设计：瞬时信号追踪系统

#### 3.2 实现连续大买单追踪器
- [ ] 编写大买单事件记录函数
- [ ] 实现3分钟内连续大买单检测逻辑
- [ ] 编写大买单金额分级评分 (500万+, 1000万+)
- [ ] 添加大买单与板块身份的组合评分逻辑
- 参考需求：3.1-3.5, 5.1-5.5
- 参考设计：瞬时信号追踪系统

#### 3.3 整合资金加速度信号
- [ ] 实现资金加速度信号与主线板块的组合判断
- [ ] 编写加速度信号的评分逻辑 (主线板块中8分)
- [ ] 添加排名跃升信号的评分逻辑 (2分)
- [ ] 测试瞬时信号的实时性和准确性
- 参考需求：3.1-3.5
- 参考设计：瞬时信号追踪系统

### 4. 板块强度量化模块

#### 4.1 实现板块地位识别
- [ ] 编写板块内资金排名计算函数
- [ ] 实现板块龙头 (第1名) 识别逻辑
- [ ] 添加板块持续性强度追踪机制
- [ ] 编写板块强势持续时间计算逻辑
- 参考需求：4.1-4.5 (板块强度与联动量化)
- 参考设计：板块强度量化模块

#### 4.2 增强主线vs支线识别
- [ ] 完善 `MAINLINE_SECTOR_LIST_BT` 和 `STRONG_SECTORS_LIST_BT` 的维护逻辑
- [ ] 实现个股与主线板块的关联判断
- [ ] 编写支线板块的评分逻辑 (2分 vs 5分)
- [ ] 添加板块切换时的历史状态清理
- 参考需求：4.1-4.5
- 参考设计：板块强度量化模块

### 5. 信号分级输出系统

#### 5.1 实现信号级别判断逻辑
- [ ] 编写A+级信号判断函数 (15分以上)
- [ ] 编写A级信号判断函数 (12分以上)  
- [ ] 编写B级信号判断函数 (8分以上)
- [ ] 实现信号级别与评分明细的记录
- 参考需求：5.1-5.5 (分级信号输出机制)
- 参考设计：信号分级阈值系统

#### 5.2 增强信号输出格式
- [ ] 修改实时信号输出，添加级别前缀 `【A+级信号】`
- [ ] 增强信号记录结构，添加 `Score`、`Score_Reasons`、`Signal_Level` 字段
- [ ] 编写评分明细的格式化显示逻辑
- [ ] 实现信号按级别排序的逻辑
- 参考需求：5.1-5.5, 6.1-6.5 (评分详情透明化)
- 参考设计：输出数据结构

### 6. 最终报告格式升级

#### 6.1 实现最终报告的分级排序
- [ ] 修改最终报告生成逻辑，按信号级别和时间双重排序
- [ ] 更新报告标题，添加版本标识 `【V7.0 报告驱动复盘】`
- [ ] 增强报告内容，显示每个信号的级别和评分明细
- [ ] 添加信号级别统计摘要 (A+级x个, A级x个, B级x个)
- 参考需求：5.1-5.5, 6.1-6.5
- 参考设计：输出数据结构

#### 6.2 完善日志和调试信息
- [ ] 更新实时日志输出格式，包含评分过程信息
- [ ] 添加调试模式下的详细评分计算日志
- [ ] 实现评分异常情况的警告和记录
- [ ] 确保日志输出的可读性和可追溯性
- 参考需求：6.1-6.5, 7.1-7.6 (兼容性与稳定性保证)
- 参考设计：错误处理

### 7. 兼容性和稳定性保障

#### 7.1 实现数据格式兼容性
- [ ] 确保现有CSV文件格式的完全兼容性
- [ ] 添加缺失字段的默认值处理逻辑
- [ ] 实现股票代码格式的统一化处理 (6位零填充)
- [ ] 测试各种数据文件命名变体的支持
- 参考需求：7.1-7.6 (兼容性与稳定性保证)
- 参考设计：数据完整性保障

#### 7.2 实现未来函数预防机制
- [ ] 审查和确保所有数据访问都是基于当前时间点或历史数据
- [ ] 添加时间序列一致性验证逻辑
- [ ] 实现数据时间戳的严格检查
- [ ] 添加未来数据访问的警告和阻止机制
- 参考需求：7.6 (完全避免未来函数)
- 参考设计：时间序列一致性

#### 7.3 错误处理和容错机制
- [ ] 增强文件读取的异常处理逻辑
- [ ] 添加数据格式异常的安全处理
- [ ] 实现评分计算中的边界条件处理
- [ ] 确保系统在部分数据缺失时的稳定运行
- 参考需求：7.1-7.6
- 参考设计：错误处理

### 8. 集成测试和验证

#### 8.1 实现回测结果验证
- [ ] 创建测试数据集，使用已知日期的历史数据
- [ ] 编写新旧版本输出结果的对比测试
- [ ] 验证信号分级的合理性和一致性
- [ ] 测试极端市场条件下的系统稳定性
- 参考需求：所有功能需求
- 参考设计：集成测试方案

#### 8.2 性能和可靠性测试
- [ ] 测试大数据量条件下的处理性能
- [ ] 验证内存使用和文件I/O效率
- [ ] 确保长时间运行的稳定性
- [ ] 验证输出结果的可重现性
- 参考需求：7.1-7.6
- 参考设计：测试策略

#### 8.3 最终集成和文档更新
- [ ] 将所有修改整合到 `backtestv6.py` 主文件
- [ ] 更新配置注释和使用说明
- [ ] 验证完整的端到端回测流程
- [ ] 生成升级后的示例输出报告
- 参考需求：所有功能需求
- 参考设计：整体架构