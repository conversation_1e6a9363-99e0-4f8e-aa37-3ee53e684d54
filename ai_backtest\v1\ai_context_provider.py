# --- START OF FILE ai_context_provider.py ---

import os
import re
import pandas as pd
from datetime import datetime


def get_market_emotional_metrics(backtest_date: str, data_dir: str, current_rank_df: pd.DataFrame,
                                 current_zt_pool_df: pd.DataFrame) -> dict:
    """
    【新增】量化市场情绪指标：昨日涨停晋级率、溢价、市场最高连板高度。
    该函数不包含未来函数，因为它只查询T-1日的数据和T日截至当前的数据。
    """
    metrics = {
        "promotion_rate": 0.0,
        "avg_premium": 0.0,
        "highest_board_count": 0,
        "yesterday_zt_leader": "无"
    }
    try:
        # 1. 计算前一个交易日
        prev_date = (datetime.strptime(backtest_date, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')
        # 简易处理周末和节假日，实际应使用交易日历
        while not os.path.exists(os.path.join(os.path.dirname(data_dir), prev_date)):
            prev_date_obj = datetime.strptime(prev_date, '%Y-%m-%d')
            if (datetime.now() - prev_date_obj).days > 10:  # 防止无限循环
                return metrics
            prev_date = (prev_date_obj - timedelta(days=1)).strftime('%Y-%m-%d')

        prev_data_dir = os.path.join(os.path.dirname(data_dir), prev_date)

        # 2. 加载昨日收盘的涨停股池
        # 找到昨日最新的涨停文件
        all_prev_day_files = os.listdir(prev_data_dir)
        zt_pool_patterns = ['zt_pool.csv', '涨停股池_akshare_东方财富_', '涨停股池_tpdog_']
        prev_zt_pool_files = [f for f in all_prev_day_files for p in zt_pool_patterns if
                              p in f and f.endswith('.csv') and 'previous_zt_pool.csv' not in f]

        if not prev_zt_pool_files:
            return metrics

        # 假设昨日收盘文件时间戳最大
        latest_prev_zt_file = sorted(prev_zt_pool_files)[-1]
        df_yesterday_zt = load_csv_safe(os.path.join(prev_data_dir, latest_prev_zt_file))

        if df_yesterday_zt is None or df_yesterday_zt.empty:
            return metrics

        code_col = next((c for c in ['代码', 'code'] if c in df_yesterday_zt.columns), None)
        name_col = next((c for c in ['名称', 'name'] if c in df_yesterday_zt.columns), None)
        board_col = next((c for c in ['连板数', 'l_info'] if c in df_yesterday_zt.columns), None)

        if not code_col: return metrics

        yesterday_zt_codes = df_yesterday_zt[code_col].astype(str).str.zfill(6).tolist()

        # 找到昨日最高板股票
        if board_col and name_col:
            df_yesterday_zt[board_col] = pd.to_numeric(df_yesterday_zt[board_col], errors='coerce').fillna(0)
            yesterday_leader_row = df_yesterday_zt.loc[df_yesterday_zt[board_col].idxmax()]
            metrics[
                'yesterday_zt_leader'] = f"{yesterday_leader_row[name_col]}({str(yesterday_leader_row[code_col]).zfill(6)})"

        # 3. 计算晋级率和平均溢价
        today_performance = current_rank_df[current_rank_df['代码'].isin(yesterday_zt_codes)]
        if not today_performance.empty:
            promoted_stocks = today_performance[today_performance['今日涨跌幅'] > 9.8]
            metrics['promotion_rate'] = len(promoted_stocks) / len(yesterday_zt_codes) if len(
                yesterday_zt_codes) > 0 else 0
            metrics['avg_premium'] = today_performance['今日涨跌幅'].mean()

        # 4. 获取今日最高连板数
        if current_zt_pool_df is not None and not current_zt_pool_df.empty:
            board_col_today = next((c for c in ['连板数', 'l_info'] if c in current_zt_pool_df.columns), None)
            if board_col_today:
                current_zt_pool_df[board_col_today] = pd.to_numeric(current_zt_pool_df[board_col_today],
                                                                    errors='coerce').fillna(0)
                metrics['highest_board_count'] = int(current_zt_pool_df[board_col_today].max())

        return metrics
    except Exception as e:
        # print(f"Error in get_market_emotional_metrics: {e}") # for debugging
        return metrics


def get_sector_peer_data(stock_code: str, stock_name: str, current_rank_df: pd.DataFrame, stock_board_map: pd.DataFrame,
                         mainline_sectors: list, strong_sectors: list, top_n=5) -> list:
    """
    【新增】获取同板块内表现最强的N个竞争对手数据。
    """
    peers_list = []
    try:
        # 1. 找到该股所属的最核心板块 (主线 > 强势 > 其他)
        stock_concepts = stock_board_map[stock_board_map['代码'] == stock_code]['概念名称'].tolist()
        if not stock_concepts:
            return ["该股无板块信息"]

        core_sector = None
        for sector in stock_concepts:
            if sector in mainline_sectors:
                core_sector = sector
                break
        if not core_sector:
            for sector in stock_concepts:
                if sector in strong_sectors:
                    core_sector = sector
                    break
        if not core_sector:
            core_sector = stock_concepts[0]

        # 2. 找到该板块下的所有股票代码
        peer_codes = stock_board_map[stock_board_map['概念名称'] == core_sector]['代码'].tolist()

        # 3. 从当前资金流排名中筛选出这些股票
        df_peers = current_rank_df[current_rank_df['代码'].isin(peer_codes)].copy()
        if df_peers.empty:
            return [f"板块[{core_sector}]内无其他可比个股"]

        # 4. 按涨跌幅和资金流排序
        df_peers_sorted = df_peers.sort_values(by=['今日涨跌幅', '今日主力净流入-净额'], ascending=[False, False])

        # 5. 格式化输出
        for _, row in df_peers_sorted.head(top_n).iterrows():
            # 跳过自己
            if row['代码'] == stock_code:
                continue
            flow_in_wan = row['今日主力净流入-净额'] / 10000
            peers_list.append(f"{row['名称']}({row['代码']}) 涨{row['今日涨跌幅']:.2f}% 流入{flow_in_wan:.0f}万")

        return peers_list if peers_list else ["板块内无其他活跃个股"]
    except Exception:
        return ["获取板块对手数据时出错"]


def get_stock_historical_context(stock_code: str, backtest_date: str, days=5) -> dict:
    """
    【新增-待实现】获取个股过去N天的历史表现。
    这是一个存根函数，实际应用中需要对接历史数据库（如SQLite, DuckDB）或历史文件。
    为保证当前代码可运行，我们返回一个默认的字典。
    """
    # TODO: 此处为未来扩展点。需要建立一个高效的历史数据查询机制。
    # 例如，可以在每日复盘结束后，将当日的收盘数据存入一个SQLite数据库，
    # 此处即可连接数据库查询过去N天的数据。

    # 当前返回默认值，以确保流程能跑通
    return {
        "avg_turnover_wan": 15000,  # 假设日均成交1.5亿
        "limit_up_count": 1,
        "failed_limit_up_count": 0
    }


# 复用 backtestv5_test.py 中的辅助函数
def find_latest_file(file_list, current_time):
    """
    从文件列表中找到最新的、但不超过当前时间的文件
    这是防止未来函数的核心机制
    """
    relevant_file = None
    for f in sorted(file_list):
        try:
            # 兼容多种时间戳格式
            # 格式1: 涨停股池_akshare_东方财富_093651.csv
            # 格式2: 09-32_news_cls.csv
            # 格式3: fund_flow_rank_20250725_103000.csv

            match = None
            # 尝试匹配 _HHMMSS.csv 格式
            match = re.search(r'_(\d{6})\.csv$', f)
            if not match:
                # 尝试匹配 HH-MM_ 格式
                match = re.search(r'(\d{2}-\d{2})_', f)
                if match:
                    time_str = match.group(1).replace('-', '') + '00'  # 09-32 -> 093200
                    f_ts = datetime.strptime(time_str, '%H%M%S').time()
                else:
                    continue
            else:
                f_ts_str = match.group(1)
                f_ts = datetime.strptime(f_ts_str, '%H%M%S').time()

            # 关键：只选择时间戳不超过当前回测时间的文件
            if f_ts <= current_time:
                relevant_file = f
            else:
                break  # 因为文件列表已排序，后续的都更晚
        except (ValueError, IndexError):
            continue
    return relevant_file

def filter_files_by_time(file_list, current_time):
    """
    过滤文件列表，只返回时间戳不超过当前时间的文件
    用于防止未来函数
    """
    valid_files = []
    for f in file_list:
        try:
            # 尝试多种时间戳格式
            # 格式1: 涨停股池_akshare_东方财富_093651.csv
            # 格式2: 09-32_news_cls.csv
            # 格式3: fund_flow_rank_20250725_103000.csv

            match = None
            # 尝试匹配 _HHMMSS.csv 格式
            match = re.search(r'_(\d{6})\.csv$', f)
            if not match:
                # 尝试匹配 HH-MM_ 格式
                match = re.search(r'(\d{2}-\d{2})_', f)
                if match:
                    time_str = match.group(1).replace('-', '') + '00'  # 09-32 -> 093200
                    f_ts = datetime.strptime(time_str, '%H%M%S').time()
                else:
                    continue
            else:
                f_ts_str = match.group(1)
                f_ts = datetime.strptime(f_ts_str, '%H%M%S').time()

            # 只保留时间戳不超过当前时间的文件
            if f_ts <= current_time:
                valid_files.append(f)
        except (ValueError, IndexError):
            continue

    return sorted(valid_files)


# 复用 fund_data_analyzer.py 中的安全加载函数
def load_csv_safe(filepath):
    try:
        # 优先使用 utf-8-sig，它可以处理带BOM的UTF-8文件
        return pd.read_csv(filepath, encoding='utf-8-sig', on_bad_lines='skip')
    except Exception:
        try:
            # 备用 gbk 编码
            return pd.read_csv(filepath, encoding='gbk', on_bad_lines='skip')
        except Exception:
            return None



def get_ai_prompt_context(
        stock_code: str,
        stock_name: str,
        current_sim_time: datetime.time,
        data_dir: str,
        market_report: str,
        stock_row_data: pd.Series,
        buy_score: int,
        hit_score_reasons: list,
        related_sectors: list,
        is_mainline: bool
) -> str:
    """
    为AI决策准备一份严格遵守时间顺序的"决策简报"(Prompt)。
    这是杜绝未来函数的核心。
    【V8.3 修正版】严格对齐fund_data_analyzer.py的文件分类逻辑
    """
    context = {}

    # 1. 基础信息 (由回测器在当前时间点直接传入)
    context['current_time'] = current_sim_time.strftime('%H:%M:%S')
    context['stock_code'] = stock_code
    context['stock_name'] = stock_name
    context['price'] = stock_row_data.get('最新价', 0)
    context['change_percent'] = stock_row_data.get('今日涨跌幅', 0)
    context['main_net_abs'] = stock_row_data.get('今日主力净流入-净额', 0)
    context['main_ratio'] = stock_row_data.get('今日主力净流入-净占比', 0)
    context['market_report'] = market_report
    context['buy_score'] = buy_score
    context['hit_score_reasons'] = " + ".join(hit_score_reasons)
    context['related_sectors'] = ", ".join(related_sectors)
    context['is_mainline'] = "是" if is_mainline else "否"

    # 2. 动态搜集历史信息 (严格使用时间过滤确保无未来函数)
    all_files_in_day = os.listdir(data_dir)

    # 搜集最新的相关新闻 (支持多种新闻源格式)
    news_files = [f for f in all_files_in_day if 'news' in f.lower() and f.endswith('.csv')]
    # 关键：过滤掉未来时间的文件
    news_files = filter_files_by_time(news_files, current_sim_time)
    latest_news_file = find_latest_file(news_files, current_sim_time)
    if latest_news_file:
        df_news = load_csv_safe(os.path.join(data_dir, latest_news_file))
        if df_news is not None and not df_news.empty:
            # 尝试不同的列名
            title_col = None
            for col in ['标题', 'title', '新闻标题', '内容']:
                if col in df_news.columns:
                    title_col = col
                    break
            if title_col:
                context['related_news'] = df_news[title_col].head(3).tolist()
            else:
                context['related_news'] = ["无最新新闻"]
        else:
            context['related_news'] = ["无最新新闻"]
    else:
        context['related_news'] = ["无最新新闻"]

    # 【核心修正】搜集最新的大单成交记录 - 严格对齐fund_data_analyzer.py
    big_deal_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑
            if f == 'ths_big_deal.csv' or 'big_deal_' in f:
                big_deal_files.append(f)

    big_deal_files = filter_files_by_time(big_deal_files, current_sim_time)
    latest_deal_file = find_latest_file(big_deal_files, current_sim_time)
    deal_summary = "无"
    if latest_deal_file:
        df_deals = load_csv_safe(os.path.join(data_dir, latest_deal_file))
        if df_deals is not None and not df_deals.empty and '股票代码' in df_deals.columns:
            # 筛选与当前股票相关的交易
            stock_deals = df_deals[df_deals['股票代码'].astype(str).str.zfill(6) == stock_code]
            if not stock_deals.empty:
                buy_count = (stock_deals['大单性质'] == '大买单').sum()
                sell_count = (stock_deals['大单性质'] == '大卖单').sum()
                deal_summary = f"有大单成交记录 (买: {buy_count}笔, 卖: {sell_count}笔)"
    context['has_big_deal'] = deal_summary

    # 【核心修正】搜集涨停股池信息 - 严格对齐fund_data_analyzer.py
    zt_pool_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑，排除previous_zt_pool.csv
            if (f == 'zt_pool.csv' or
                '涨停股池_akshare_东方财富_' in f or
                '涨停股池_tpdog_' in f) and 'previous_zt_pool.csv' not in f:
                zt_pool_files.append(f)

    zt_pool_files = filter_files_by_time(zt_pool_files, current_sim_time)
    latest_zt_file = find_latest_file(zt_pool_files, current_sim_time)
    zt_info = "无涨停股池数据"
    zt_stocks_list = []
    if latest_zt_file:
        df_zt = load_csv_safe(os.path.join(data_dir, latest_zt_file))
        if df_zt is not None and not df_zt.empty:
            # 检查当前股票是否在涨停股池中
            code_col = None
            name_col = None
            for col in ['代码', 'code', '股票代码']:
                if col in df_zt.columns:
                    code_col = col
                    break
            for col in ['名称', 'name', '股票名称']:
                if col in df_zt.columns:
                    name_col = col
                    break

            if code_col and name_col:
                # 获取涨停股票列表
                zt_stocks_list = []
                for _, row in df_zt.iterrows():
                    code = str(row[code_col]).zfill(6)
                    name = row[name_col]
                    zt_stocks_list.append(f"{name}({code})")

                # 检查当前股票是否在涨停股池中
                stock_in_zt = df_zt[df_zt[code_col].astype(str).str.zfill(6) == stock_code]
                if not stock_in_zt.empty:
                    zt_info = f"该股票在涨停股池中 (共{len(df_zt)}只涨停股)"
                else:
                    zt_info = f"该股票不在涨停股池中 (共{len(df_zt)}只涨停股)"
            else:
                zt_info = f"涨停股池数据格式异常 (共{len(df_zt)}只股票)"
    context['zt_pool_info'] = zt_info
    context['zt_stocks_list'] = zt_stocks_list

    # 【核心修正】搜集炸板股池信息 - 严格对齐fund_data_analyzer.py
    zb_pool_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑：'炸板股池_akshare_东方财富_'
            if '炸板股池_akshare_东方财富_' in f:
                zb_pool_files.append(f)

    zb_pool_files = filter_files_by_time(zb_pool_files, current_sim_time)
    latest_zb_file = find_latest_file(zb_pool_files, current_sim_time)
    zb_info = "无炸板股池数据"
    zb_stocks_list = []
    if latest_zb_file:
        df_zb = load_csv_safe(os.path.join(data_dir, latest_zb_file))
        if df_zb is not None and not df_zb.empty:
            # 检查当前股票是否在炸板股池中
            code_col = None
            name_col = None
            for col in ['代码', 'code', '股票代码']:
                if col in df_zb.columns:
                    code_col = col
                    break
            for col in ['名称', 'name', '股票名称']:
                if col in df_zb.columns:
                    name_col = col
                    break

            if code_col and name_col:
                # 获取炸板股票列表
                zb_stocks_list = []
                for _, row in df_zb.iterrows():
                    code = str(row[code_col]).zfill(6)
                    name = row[name_col]
                    zb_times = row.get('炸板次数', 0) if '炸板次数' in df_zb.columns else 0
                    zb_stocks_list.append(f"{name}({code},炸{zb_times}次)")

                # 检查当前股票是否在炸板股池中
                stock_in_zb = df_zb[df_zb[code_col].astype(str).str.zfill(6) == stock_code]
                if not stock_in_zb.empty:
                    zb_info = f"该股票在炸板股池中 (共{len(df_zb)}只炸板股)"
                else:
                    zb_info = f"该股票不在炸板股池中 (共{len(df_zb)}只炸板股)"
            else:
                zb_info = f"炸板股池数据格式异常 (共{len(df_zb)}只股票)"
    context['zb_pool_info'] = zb_info
    context['zb_stocks_list'] = zb_stocks_list

    # 【核心修正】搜集行业资金流数据 - 严格对齐fund_data_analyzer.py
    sector_flow_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑：'sector_fund_flow_tpdog.csv', 'sector_fund_flow_akshare.csv', 'sector_fund_flow_rank_'
            if (f == 'sector_fund_flow_tpdog.csv' or
                f == 'sector_fund_flow_akshare.csv' or
                'sector_fund_flow_rank_' in f):
                sector_flow_files.append(f)

    sector_flow_files = filter_files_by_time(sector_flow_files, current_sim_time)
    latest_sector_flow_file = find_latest_file(sector_flow_files, current_sim_time)
    sector_flow_top100 = []
    if latest_sector_flow_file:
        df_sector_flow = load_csv_safe(os.path.join(data_dir, latest_sector_flow_file))
        if df_sector_flow is not None and not df_sector_flow.empty:
            # 按资金净流入排序，取前100名
            # 适配不同的列名格式
            name_col = None
            flow_col = None
            ratio_col = None

            # 查找名称列 - 行业资金流文件使用'名称'列
            for col in ['名称', 'name', '行业', 'sector']:
                if col in df_sector_flow.columns:
                    name_col = col
                    break

            # 查找资金流列
            for col in ['今日主力净流入-净额', '净额', '净流入', 'net_flow']:
                if col in df_sector_flow.columns:
                    flow_col = col
                    break

            # 查找比例列
            for col in ['今日主力净流入-净占比', '净占比', '涨跌幅', '行业-涨跌幅']:
                if col in df_sector_flow.columns:
                    ratio_col = col
                    break

            if name_col and flow_col:
                df_sorted = df_sector_flow.sort_values(flow_col, ascending=False)
                for i, (_, row) in enumerate(df_sorted.head(100).iterrows()):
                    name = row[name_col]
                    flow = row[flow_col]
                    ratio = row.get(ratio_col, 0) if ratio_col else 0
                    # 处理不同的数据格式
                    if isinstance(flow, str):
                        # 如果是字符串格式，尝试转换
                        try:
                            flow = float(flow.replace('万', '').replace('亿', '')) * 10000 if '万' in str(
                                flow) else float(flow)
                        except:
                            flow = 0
                    sector_flow_top100.append(f"{i + 1}.{name}({flow / 100000000:.2f}亿,{ratio:.2f}%)")
    context['sector_flow_top100'] = sector_flow_top100

    # 【核心修正】搜集概念资金流数据 - 严格对齐fund_data_analyzer.py
    concept_flow_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑：'concept_fund_flow_tpdog.csv', 'concept_fund_flow_akshare.csv', 'concept_fund_flow_'
            if (f == 'concept_fund_flow_tpdog.csv' or
                f == 'concept_fund_flow_akshare.csv' or
                'concept_fund_flow_' in f):
                concept_flow_files.append(f)

    concept_flow_files = filter_files_by_time(concept_flow_files, current_sim_time)
    latest_concept_flow_file = find_latest_file(concept_flow_files, current_sim_time)
    concept_flow_top100 = []
    if latest_concept_flow_file:
        df_concept_flow = load_csv_safe(os.path.join(data_dir, latest_concept_flow_file))
        if df_concept_flow is not None and not df_concept_flow.empty:
            # 按资金净流入排序，取前100名
            # 适配不同的列名格式
            name_col = None
            flow_col = None
            ratio_col = None

            # 查找名称列
            for col in ['名称', '行业', 'name']:
                if col in df_concept_flow.columns:
                    name_col = col
                    break

            # 查找资金流列
            for col in ['今日主力净流入-净额', '净额', '净流入', 'net_flow']:
                if col in df_concept_flow.columns:
                    flow_col = col
                    break

            # 查找比例列
            for col in ['今日主力净流入-净占比', '净占比', '涨跌幅', '行业-涨跌幅']:
                if col in df_concept_flow.columns:
                    ratio_col = col
                    break

            if name_col and flow_col:
                df_sorted = df_concept_flow.sort_values(flow_col, ascending=False)
                for i, (_, row) in enumerate(df_sorted.head(100).iterrows()):
                    name = row[name_col]
                    flow = row[flow_col]
                    ratio = row.get(ratio_col, 0) if ratio_col else 0
                    # 处理不同的数据格式
                    if isinstance(flow, str):
                        # 如果是字符串格式，尝试转换
                        try:
                            flow = float(flow.replace('万', '').replace('亿', '')) * 10000 if '万' in str(
                                flow) else float(flow)
                        except:
                            flow = 0
                    concept_flow_top100.append(f"{i + 1}.{name}({flow / 100000000:.2f}亿,{ratio:.2f}%)")
    context['concept_flow_top100'] = concept_flow_top100

    # 搜集新闻数据 - 支持多个新闻源
    news_patterns = ['news_cls.csv', 'news_em.csv', 'news_ths.csv']
    all_news = []
    for pattern in news_patterns:
        news_files = [f for f in all_files_in_day if pattern in f and f.endswith('.csv')]
        news_files = filter_files_by_time(news_files, current_sim_time)
        latest_news_file = find_latest_file(news_files, current_sim_time)

        if latest_news_file:
            df_news = load_csv_safe(os.path.join(data_dir, latest_news_file))
            if df_news is not None and not df_news.empty:
                # 查找标题和内容列
                title_col = None
                content_col = None
                time_col = None

                for col in ['标题', 'title', '新闻标题', 'headline']:
                    if col in df_news.columns:
                        title_col = col
                        break

                for col in ['内容', 'content', '新闻内容', 'summary', '摘要']:
                    if col in df_news.columns:
                        content_col = col
                        break

                for col in ['时间', 'time', '发布时间', 'publish_time']:
                    if col in df_news.columns:
                        time_col = col
                        break

                if title_col:
                    # 获取最新的几条新闻
                    for _, row in df_news.head(5).iterrows():
                        title = row[title_col]
                        content = row.get(content_col, '') if content_col else ''
                        news_time = row.get(time_col, '') if time_col else ''

                        # 组合新闻信息
                        news_item = f"{title}"
                        if content and len(str(content)) > 0:
                            news_item += f" - {str(content)[:100]}..."
                        if news_time:
                            news_item += f" ({news_time})"

                        all_news.append(news_item)

    context['latest_news'] = all_news[:10]  # 最多保留10条最新新闻

    # 【核心修正】搜集大笔订单数据 - 严格对齐fund_data_analyzer.py
    movers_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑：'movers_大笔买入.csv', 'movers_有大买盘.csv'
            if f == 'movers_大笔买入.csv' or f == 'movers_有大买盘.csv':
                movers_files.append(f)

    big_orders = []
    current_stock_big_orders = []

    movers_files = filter_files_by_time(movers_files, current_sim_time)
    latest_movers_file = find_latest_file(movers_files, current_sim_time)

    if latest_movers_file:
        df_movers = load_csv_safe(os.path.join(data_dir, latest_movers_file))
        if df_movers is not None and not df_movers.empty:
            # 查找股票代码和名称列
            code_col = None
            name_col = None

            for col in ['代码', 'code', '股票代码']:
                if col in df_movers.columns:
                    code_col = col
                    break

            for col in ['名称', 'name', '股票名称']:
                if col in df_movers.columns:
                    name_col = col
                    break

            if code_col and name_col:
                # 检查当前股票是否在大笔订单中
                stock_in_movers = df_movers[df_movers[code_col].astype(str).str.zfill(6) == stock_code]
                if not stock_in_movers.empty:
                    order_type = "大笔买入" if "大笔买入" in latest_movers_file else "有大买盘"
                    current_stock_big_orders.append(f"该股票出现{order_type}信号")

                # 获取所有大笔订单股票列表
                for _, row in df_movers.head(20).iterrows():
                    code = str(row[code_col]).zfill(6)
                    name = row[name_col]
                    order_type = "大笔买入" if "大笔买入" in latest_movers_file else "大买盘"
                    big_orders.append(f"{name}({code},{order_type})")

    context['big_orders'] = big_orders
    context['current_stock_big_orders'] = current_stock_big_orders

    # 【核心修正】搜集个股资金流数据 - 严格对齐fund_data_analyzer.py，显示100名
    individual_flow_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑：'fund_flow_rank_', 'individual_fund_flow_'
            # 关键修正：必须排除板块资金流文件，避免混淆
            if ('sector' not in f and 'concept' not in f and
                ('fund_flow_rank_' in f or 'individual_fund_flow_' in f)):
                individual_flow_files.append(f)

    individual_flow_files = filter_files_by_time(individual_flow_files, current_sim_time)
    latest_individual_flow_file = find_latest_file(individual_flow_files, current_sim_time)
    individual_flow_top100 = []
    current_stock_rank_info = "未找到该股票的资金流排名"
    if latest_individual_flow_file:
        df_individual_flow = load_csv_safe(os.path.join(data_dir, latest_individual_flow_file))
        if df_individual_flow is not None and not df_individual_flow.empty:
            # 适配不同的列名格式
            code_col = None
            name_col = None
            flow_col = None
            ratio_col = None
            change_col = None

            # 查找代码列 - 根据实际文件格式调整
            for col in ['代码', 'code', '股票代码', '证券代码']:
                if col in df_individual_flow.columns:
                    code_col = col
                    break

            # 查找名称列 - 根据实际文件格式调整
            for col in ['名称', 'name', '股票名称', '证券名称', '股票简称']:
                if col in df_individual_flow.columns:
                    name_col = col
                    break

            # 查找资金流列 - 根据实际文件格式调整
            for col in ['今日主力净流入-净额', '净额', '净流入', 'net_flow', '主力净流入', '主力净额']:
                if col in df_individual_flow.columns:
                    flow_col = col
                    break

            # 查找比例列 - 根据实际文件格式调整
            for col in ['今日主力净流入-净占比', '净占比', '主力净占比', '主力占比']:
                if col in df_individual_flow.columns:
                    ratio_col = col
                    break

            # 查找涨跌幅列 - 根据实际文件格式调整
            for col in ['今日涨跌幅', '涨跌幅', 'change_pct', '涨跌']:
                if col in df_individual_flow.columns:
                    change_col = col
                    break

            if code_col and name_col and flow_col:
                # 确保代码列格式统一
                df_individual_flow[code_col] = df_individual_flow[code_col].astype(str).str.zfill(6)

                # 确保资金流列是数值类型
                df_individual_flow[flow_col] = pd.to_numeric(df_individual_flow[flow_col], errors='coerce').fillna(0)

                # 按资金净流入排序
                df_sorted = df_individual_flow.sort_values(by=flow_col, ascending=False).reset_index(drop=True)

                # 查找当前股票的排名
                current_stock_data = df_sorted[df_sorted[code_col] == stock_code]
                if not current_stock_data.empty:
                    current_rank = current_stock_data.index[0] + 1
                    current_flow = current_stock_data.iloc[0][flow_col]
                    current_ratio = current_stock_data.iloc[0].get(ratio_col, 0) if ratio_col else 0
                    current_change = current_stock_data.iloc[0].get(change_col, 0) if change_col else 0
                    current_stock_rank_info = f"该股票资金流排名第{current_rank}位，净流入{current_flow / 10000:.2f}万元"
                    if ratio_col and pd.notna(current_ratio):
                        current_stock_rank_info += f"，净占比{pd.to_numeric(current_ratio, errors='coerce'):.2f}%"
                    if change_col and pd.notna(current_change):
                        current_stock_rank_info += f"，涨跌幅{pd.to_numeric(current_change, errors='coerce'):.2f}%"

                # 【关键修改】生成前100名个股资金流排行榜
                for i, (_, row) in enumerate(df_sorted.head(100).iterrows()):
                    code = row[code_col]
                    name = row[name_col]
                    flow = row[flow_col]
                    ratio = row.get(ratio_col, 0) if ratio_col else 0
                    change = row.get(change_col, 0) if change_col else 0

                    rank_info = f"{i + 1}.{name}({code})"
                    rank_info += f" 净流入{flow / 10000:.2f}万"
                    if ratio_col and pd.notna(ratio):
                        rank_info += f" 净占比{pd.to_numeric(ratio, errors='coerce'):.2f}%"
                    if change_col and pd.notna(change):
                        rank_info += f" 涨跌{pd.to_numeric(change, errors='coerce'):.2f}%"

                    individual_flow_top100.append(rank_info)

    context['individual_flow_top100'] = individual_flow_top100
    context['current_stock_rank_info'] = current_stock_rank_info

    # ... 在此可以不断扩充，搜集更多类型的数据，只要遵循 find_latest_file 原则即可 ...

    # 3. 组装成最终的Prompt (返回一个字符串)
    # 注意：这里的 {format_instructions} 是一个占位符，LangChain会自动填充
    prompt_string = f"""
你是一位顶级的A股超短线交易大师，你的交易哲学和分析框架深度融合了**"华东大导弹"、"赵老哥"、"涅盘重升"、"著名刺客"、"龙飞虎"、"小鳄鱼"、"92科比"、"榜中榜"、"孤独牛背"、"凡倍无名"等多位顶级游资的核心思想。你的思维核心是周期、情绪、人性与预期差**。你追求的不是预测，而是跟随、应对与先手。。你的决策必须果断、理性，并给出清晰的理由。

现在是交易日【{context['current_time']}】，请基于以下信息，对股票【{context['stock_name']} ({context['stock_code']})】做出“买入(BUY)”、“观察(HOLD)”或“放弃(REJECT)”的决策，并以JSON格式返回你的分析。

**一、 市场宏观背景 (当前市场整体态势):**
{context['market_report']}

**二、 个股核心数据:**
- **股票信息**: {context['stock_name']} ({context['stock_code']})
- **当前价格**: {context['price']:.2f}元 (今日涨跌幅: {context['change_percent']:.2f}%)
- **核心资金**: 主力净流入 {context['main_net_abs'] / 10000:.0f}万元 (净占比: {context['main_ratio']}%)
- **板块归属**: {context['related_sectors']}
- **是否主线**: {context['is_mainline']}

**三、 我的量化规则初步判断 (系统初筛结果):**
- **综合评分**: {context['buy_score']}
- **加分项**: {context['hit_score_reasons']}

**四、 其他盘中重要信号:**
- **最新新闻**: {chr(100).join(context['latest_news'][:10]) if context['latest_news'] else '无最新新闻'}
- **大单活动**: {context['has_big_deal']}
- **大笔订单**: {', '.join(context['current_stock_big_orders']) if context['current_stock_big_orders'] else '该股票无大笔订单信号'}
- **涨停股池**: {context['zt_pool_info']}
  涨停股票列表: {', '.join(context['zt_stocks_list'][:100]) if context['zt_stocks_list'] else '无'}
- **炸板股池**: {context['zb_pool_info']}
  炸板股票列表: {', '.join(context['zb_stocks_list'][:20]) if context['zb_stocks_list'] else '无'}

**五、 个股资金流情况:**
- 当前股票排名: {context['current_stock_rank_info']}
- 市场前100名: {chr(10).join(['  ' + item for item in context['individual_flow_top100'][:100]]) if context['individual_flow_top100'] else '无个股资金流数据'}

**六、 行业资金流排行榜(前20名):**
{chr(10).join(context['sector_flow_top100'][:20]) if context['sector_flow_top100'] else '无行业资金流数据'}

**七、 概念资金流排行榜(前20名):**
{chr(10).join(context['concept_flow_top100'][:20]) if context['concept_flow_top100'] else '无概念资金流数据'}

**八、 大笔订单活跃股票(前20名):**
{', '.join(context['big_orders'][:20]) if context['big_orders'] else '无大笔订单数据'}

**九、 你的决策任务:**
结合以上所有信息，特别是**市场宏观背景**和个股在其中的**相对地位**，给出你的最终交易决策。
思考路径应为：
1. 这只股票是否处于当前市场最强的风口（主线）上？
2. 它的资金流入和技术形态，是否支撑它成为这个风口的龙头？
3. 对比我的量化评分，你是否有补充或否定的观点？
4. 综合判断，现在是买入的最佳时机吗？风险收益比如何？

请严格按照下面的JSON格式输出你的决策，不要有任何多余的文字。
{{format_instructions}}
"""
    return prompt_string