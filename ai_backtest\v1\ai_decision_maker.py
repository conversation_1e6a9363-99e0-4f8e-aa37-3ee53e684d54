import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
# 【V7.1 修复】修复LangChain的Pydantic v1弃用警告
from pydantic.v1 import BaseModel, Field
import time

# --- 【V7.1 核心修改】从 .env 文件加载环境变量 ---
load_dotenv()

# 1. 定义AI决策的输出格式 (Pydantic模型)
class AIDecision(BaseModel):
    decision: str = Field(description="你的最终决策，必须是 'BUY', 'HOLD', 'REJECT' 中的一个")
    confidence: float = Field(description="你对这个决策的信心指数，从 0.0 到 1.0")
    reasoning: str = Field(description="做出该决策的核心理由，解释为什么买或为什么不买，限50字以内")
    risk_assessment: str = Field(description="简要说明该决策可能面临的主要风险，限30字以内")

# 2. 初始化语言模型 (LLM)，这里配置为指向SiliconFlow的DeepSeek
# --- 【V7.1 核心修改】从环境变量中读取API Key ---
SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY")

if not SILICONFLOW_API_KEY:
    raise ValueError("错误：未在.env文件中找到 SILICONFLOW_API_KEY。请在项目根目录创建 .env 文件并添加 SILICONFLOW_API_KEY = 'sk-your_key'")

llm = ChatOpenAI(
    model="deepseek-ai/DeepSeek-V3",  # 【性能优化】从R1换成V3，速度提升7.9倍（从22秒降到3秒）
    openai_api_key=SILICONFLOW_API_KEY,
    openai_api_base="https://api.siliconflow.cn/v1",
    temperature=0.1,  # 对于需要精确JSON输出的任务，建议使用较低的温度
    max_tokens=256,   # 【优化】减少token数量，加快响应
    request_timeout=15 # 【优化】减少超时时间，从60秒降到15秒
)

# 3. 创建输出解析器，它会告诉LLM如何格式化输出
parser = JsonOutputParser(pydantic_object=AIDecision)

# 4. 创建Prompt模板，并把解析器的格式指令部分嵌入
prompt_template = PromptTemplate(
    template="{prompt_body}\n{format_instructions}\n",
    input_variables=["prompt_body"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

# 5. 将所有组件链接成一个Chain
decision_chain = prompt_template | llm | parser

# 友情提示：请确保在 ai_decision_maker.py 文件顶部已导入 time 模块
# import time

def get_ai_decision(prompt_context_string: str):
    """
    接收由情报秘书生成的完整Prompt字符串，调用LLM并返回结构化的决策字典。
    【V8.4 修正版】增加了多API轮询和余额不足处理机制。
    """
    from api_manager import api_manager

    max_retries = 3
    initial_delay = 5  # 初始等待5秒

    # 尝试所有可用的API密钥
    api_attempts = 0
    max_api_attempts = len(api_manager.api_keys)

    while api_attempts < max_api_attempts:
        try:
            # 获取当前可用的API密钥
            current_api_key = api_manager.get_available_api_key()

            # 使用当前API密钥创建新的LLM实例
            from langchain_openai import ChatOpenAI
            current_llm = ChatOpenAI(
                model="deepseek-ai/DeepSeek-V3",
                openai_api_key=current_api_key,
                openai_api_base="https://api.siliconflow.cn/v1",
                temperature=0.1,
                max_tokens=256,
                request_timeout=15
            )

            # 创建新的chain
            current_chain = prompt_template | current_llm | parser

            # 对当前API密钥进行重试
            for retry_attempt in range(max_retries):
                try:
                    # 调用链，传入Prompt主体内容
                    ai_response = current_chain.invoke({"prompt_body": prompt_context_string})
                    return ai_response
                except Exception as e:
                    error_message = str(e)

                    # 检查是否是余额不足错误
                    if api_manager.is_insufficient_balance_error(error_message):
                        print(f"!!! API密钥余额不足: {current_api_key[:10]}...")
                        api_manager.mark_key_insufficient(current_api_key)
                        break  # 跳出重试循环，尝试下一个API密钥

                    # 检查是否是速率限制错误
                    elif '429' in error_message:
                        wait_time = initial_delay * (2 ** retry_attempt)  # 指数退避
                        print(
                            f"!!! 遭遇AI Trader API速率限制 (429)。将在 {wait_time} 秒后进行第 {retry_attempt + 1}/{max_retries} 次重试...")
                        time.sleep(wait_time)
                        continue  # 继续下一次重试
                    else:
                        # 如果是其他错误，则直接失败
                        print(f"!!! 调用AI决策时发生严重错误: {e}")
                        return {
                            "decision": "ERROR",
                            "confidence": 0.0,
                            "reasoning": f"AI调用异常: {str(e)[:100]}",
                            "risk_assessment": "未知"
                        }

            # 当前API密钥的所有重试都失败了，尝试下一个API密钥
            api_attempts += 1

        except Exception as e:
            if "所有API密钥余额不足" in str(e):
                print(f"!!! 所有API密钥余额不足，无法继续调用AI Trader。")
                return {
                    "decision": "ERROR",
                    "confidence": 0.0,
                    "reasoning": "所有API密钥余额不足",
                    "risk_assessment": "无法获取AI决策"
                }
            else:
                print(f"!!! 获取API密钥时发生错误: {e}")
                api_attempts += 1

    # 如果所有API密钥都尝试过了仍然失败
    print(f"!!! AI Trader 在尝试所有 {max_api_attempts} 个API密钥后仍然失败，放弃本次决策。")
    return {
        "decision": "ERROR",
        "confidence": 0.0,
        "reasoning": "AI因所有API密钥不可用调用失败",
        "risk_assessment": "网络或服务异常"
    }