import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from pydantic.v1 import BaseModel, Field
from typing import List
import time

# --- 从 .env 文件加载环境变量 ---
load_dotenv()


# 1. 定义AI侦察员的输出格式
class PotentialStock(BaseModel):
    """定义单个潜力股的结构"""
    stock_code: str = Field(description="股票代码，格式为6位数字字符串")
    stock_name: str = Field(description="股票名称")
    reasoning: str = Field(description="识别该股票为潜力的核心逻辑，限50字以内")


class ScoutAnalysis(BaseModel):
    """定义侦察员的完整分析报告结构"""
    potential_stocks: List[PotentialStock] = Field(description="识别出的潜力股列表，最多5只")
    market_summary: str = Field(description="对当前盘面核心热点和资金流向的简要总结，限50字以内")


# 2. 初始化语言模型 (与交易员使用相同的配置)
SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY")

if not SILICONFLOW_API_KEY:
    raise ValueError("错误：未在.env文件中找到 SILICONFLOW_API_KEY。")

llm = ChatOpenAI(
    model="deepseek-ai/DeepSeek-V3",
    openai_api_key=SILICONFLOW_API_KEY,
    openai_api_base="https://api.siliconflow.cn/v1",
    temperature=0.2,  # 分析任务可略微提高温度
    max_tokens=1024,  # 分析任务需要更多Token
    request_timeout=30  # 复杂任务需要更长的超时时间
)

# 3. 创建输出解析器
scout_parser = JsonOutputParser(pydantic_object=ScoutAnalysis)

# 4. 创建Prompt模板
scout_prompt_template = PromptTemplate(
    template="{prompt_body}\n{format_instructions}\n",
    input_variables=["prompt_body"],
    partial_variables={"format_instructions": scout_parser.get_format_instructions()},
)

# 5. 构建Chain
scout_chain = scout_prompt_template | llm | scout_parser


def get_potential_stocks_from_ai(prompt_context_string: str):
    """
    接收由侦察员情报秘书生成的市场全局Prompt，调用LLM并返回结构化的潜力股列表。
    【V8.4 修正版】增加了多API轮询和余额不足处理机制。
    """
    from api_manager import api_manager

    max_retries = 3
    initial_delay = 5  # 初始等待5秒

    # 尝试所有可用的API密钥
    api_attempts = 0
    max_api_attempts = len(api_manager.api_keys)

    while api_attempts < max_api_attempts:
        try:
            # 获取当前可用的API密钥
            current_api_key = api_manager.get_available_api_key()

            # 使用当前API密钥创建新的LLM实例
            from langchain_openai import ChatOpenAI
            current_llm = ChatOpenAI(
                model="deepseek-ai/DeepSeek-V3",
                openai_api_key=current_api_key,
                openai_api_base="https://api.siliconflow.cn/v1",
                temperature=0.2,
                max_tokens=1024,
                request_timeout=30
            )

            # 创建新的chain
            current_chain = scout_prompt_template | current_llm | scout_parser

            # 对当前API密钥进行重试
            for retry_attempt in range(max_retries):
                try:
                    # 调用链，传入Prompt主体内容
                    ai_response = current_chain.invoke({"prompt_body": prompt_context_string})
                    return ai_response
                except Exception as e:
                    error_message = str(e)

                    # 检查是否是余额不足错误
                    if api_manager.is_insufficient_balance_error(error_message):
                        print(f"!!! API密钥余额不足: {current_api_key[:10]}...")
                        api_manager.mark_key_insufficient(current_api_key)
                        break  # 跳出重试循环，尝试下一个API密钥

                    # 检查是否是速率限制错误
                    elif '429' in error_message:
                        wait_time = initial_delay * (2 ** retry_attempt)  # 指数退避
                        print(
                            f"!!! 遭遇AI Scout API速率限制 (429)。将在 {wait_time} 秒后进行第 {retry_attempt + 1}/{max_retries} 次重试...")
                        time.sleep(wait_time)
                        continue  # 继续下一次重试
                    else:
                        # 如果是其他错误，则直接失败
                        print(f"!!! 调用AI Scout时发生严重错误: {e}")
                        return {
                            "potential_stocks": [],
                            "market_summary": f"AI Scout调用异常: {e}"
                        }

            # 当前API密钥的所有重试都失败了，尝试下一个API密钥
            api_attempts += 1

        except Exception as e:
            if "所有API密钥余额不足" in str(e):
                print(f"!!! 所有API密钥余额不足，无法继续调用AI Scout。")
                return {
                    "potential_stocks": [],
                    "market_summary": "所有API密钥余额不足"
                }
            else:
                print(f"!!! 获取API密钥时发生错误: {e}")
                api_attempts += 1

    # 如果所有API密钥都尝试过了仍然失败
    print(f"!!! AI Scout 在尝试所有 {max_api_attempts} 个API密钥后仍然失败，放弃本次调用。")
    return {
        "potential_stocks": [],
        "market_summary": "AI Scout因所有API密钥不可用调用失败"
    }