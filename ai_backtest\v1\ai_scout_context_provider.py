import os
import pandas as pd
from datetime import datetime
# 复用现有的辅助函数，确保一致性
from ai_context_provider import find_latest_file, filter_files_by_time, load_csv_safe


def get_scout_prompt_context(
        current_sim_time: datetime.time,
        data_dir: str,
        market_report: str
) -> str:
    """
    为AI Scout准备一份严格遵守时间顺序的"市场全局情报"(Prompt)。
    V8.3版：增强了盘口异动信息的详细解析，支持相关信息字段的具体数值显示。
    """
    context = {}
    all_files_in_day = os.listdir(data_dir)

    # 1. 基础信息
    context['current_time'] = current_sim_time.strftime('%H:%M:%S')
    context['market_report'] = market_report  # 主线/支线分析报告

    # 2. 搜集涨停股池信息
    zt_pool_patterns = ['zt_pool.csv', '涨停股池_akshare_东方财富_', '涨停股池_tpdog_']
    zt_pool_files = [f for f in all_files_in_day for p in zt_pool_patterns if p in f and f.endswith('.csv') and 'previous_zt_pool.csv' not in f]
    zt_pool_files = filter_files_by_time(zt_pool_files, current_sim_time)
    latest_zt_file = find_latest_file(zt_pool_files, current_sim_time)
    zt_stocks_list = []
    if latest_zt_file:
        df_zt = load_csv_safe(os.path.join(data_dir, latest_zt_file))
        if df_zt is not None and not df_zt.empty:
            code_col = next((c for c in ['代码', 'code'] if c in df_zt.columns), None)
            name_col = next((c for c in ['名称', 'name'] if c in df_zt.columns), None)
            board_col = next((c for c in ['连板数', 'l_info'] if c in df_zt.columns), None)
            if code_col and name_col:
                for _, row in df_zt.head(100).iterrows():
                    board_info = f"({row[board_col]}板)" if board_col and pd.notna(row.get(board_col)) else ""
                    stock_code = str(row[code_col]).zfill(6)
                    zt_stocks_list.append(f"{row[name_col]}({stock_code}){board_info}")
    context['zt_stocks_list'] = zt_stocks_list if zt_stocks_list else ["无"]

    # 3. 搜集炸板股池信息
    zb_pool_patterns = ['炸板股池_akshare_东方财富_']
    zb_pool_files = [f for f in all_files_in_day for p in zb_pool_patterns if p in f and f.endswith('.csv')]
    zb_pool_files = filter_files_by_time(zb_pool_files, current_sim_time)
    latest_zb_file = find_latest_file(zb_pool_files, current_sim_time)
    zb_stocks_list = []
    if latest_zb_file:
        df_zb = load_csv_safe(os.path.join(data_dir, latest_zb_file))
        if df_zb is not None and not df_zb.empty:
            name_col = next((c for c in ['名称', 'name'] if c in df_zb.columns), None)
            code_col = next((c for c in ['代码', 'code'] if c in df_zb.columns), None)
            if name_col and code_col:
                for _, row in df_zb.head(100).iterrows():
                    stock_code = str(row[code_col]).zfill(6)
                    zb_stocks_list.append(f"{row[name_col]}({stock_code})")
    context['zb_stocks_list'] = zb_stocks_list if zb_stocks_list else ["无"]

    # 4. 【核心修正】搜集个股资金流排名 - 对齐fund_data_analyzer.py
    # 明确排除板块资金流文件，避免混淆
    individual_flow_patterns = ['fund_flow_rank_', 'individual_fund_flow_']
    individual_flow_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 必须不包含'sector'或'concept'，这才是区分个股和板块的关键
            if 'sector' not in f and 'concept' not in f:
                for p in individual_flow_patterns:
                    if p in f:
                        individual_flow_files.append(f)
                        break  # 避免重复添加

    individual_flow_files = filter_files_by_time(individual_flow_files, current_sim_time)
    latest_individual_file = find_latest_file(individual_flow_files, current_sim_time)
    individual_flow_list = []
    if latest_individual_file:
        df_ind = load_csv_safe(os.path.join(data_dir, latest_individual_file))
        if df_ind is not None and not df_ind.empty:
            name_col = next((c for c in ['名称', 'name', '股票简称'] if c in df_ind.columns), None)
            code_col = next((c for c in ['代码', 'code', '股票代码'] if c in df_ind.columns), None)
            flow_col = next((c for c in ['今日主力净流入-净额', '净流入'] if c in df_ind.columns), None)
            if name_col and flow_col:
                df_ind[flow_col] = pd.to_numeric(df_ind[flow_col], errors='coerce').fillna(0)
                if code_col:
                    df_ind[code_col] = df_ind[code_col].astype(str).str.zfill(6)
                df_sorted = df_ind.sort_values(by=flow_col, ascending=False)
                for i, row in df_sorted.head(100).iterrows():
                    flow_in_wan = row[flow_col] / 10000
                    stock_code = str(row[code_col]).zfill(6) if code_col else "000000"
                    individual_flow_list.append(f"{i + 1}.{row[name_col]}({stock_code})({flow_in_wan:.0f}万)")
    context['individual_flow_list'] = individual_flow_list if individual_flow_list else ["无"]

    # 5. 【核心升级】搜集盘口异动信息 - 支持详细的相关信息解析
    def parse_movers_detail_info(info_str):
        """解析相关信息字段，格式：124230,19.24000,0.300000 (手数,价格,涨跌幅)"""
        try:
            if pd.isna(info_str) or str(info_str).strip() == '':
                return ""

            parts = str(info_str).split(',')
            if len(parts) >= 3:
                # 格式为：成交手数,价格,涨跌幅
                volume_hands = int(float(parts[0].strip()))
                price = float(parts[1].strip())
                change_ratio = float(parts[2].strip())

                # 格式化成交手数显示
                if volume_hands >= 10000:
                    volume_display = f"{volume_hands / 10000:.1f}万手"
                elif volume_hands >= 1000:
                    volume_display = f"{volume_hands / 1000:.1f}千手"
                else:
                    volume_display = f"{volume_hands}手"

                return f"[{volume_display}|{price:.2f}|{change_ratio:.2f}%]"
            else:
                return f"[{info_str}]"
        except:
            return f"[{info_str}]" if info_str else ""

    movers_big_buy_list = []
    movers_heavy_buy_list = []

    # 大笔买入 - 支持多种文件格式
    big_buy_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑
            if f == 'movers_大笔买入.csv':
                big_buy_files.append(f)

    big_buy_files = filter_files_by_time(big_buy_files, current_sim_time)
    latest_big_buy_file = find_latest_file(big_buy_files, current_sim_time)
    if latest_big_buy_file:
        df_big_buy = load_csv_safe(os.path.join(data_dir, latest_big_buy_file))
        if df_big_buy is not None and not df_big_buy.empty:
            # 适配不同的列名格式
            name_col = next((c for c in ['名称', 'name'] if c in df_big_buy.columns), None)
            code_col = next((c for c in ['代码', 'code'] if c in df_big_buy.columns), None)
            time_col = next((c for c in ['时间', 'time'] if c in df_big_buy.columns), None)
            info_col = next((c for c in ['相关信息', 'info', '相关数据'] if c in df_big_buy.columns), None)

            if name_col:
                for _, row in df_big_buy.head(50).iterrows():  # 显示前50条
                    stock_code = str(row[code_col]).zfill(6) if code_col and pd.notna(row.get(code_col)) else ""

                    # 解析相关信息
                    detail_info = ""
                    if info_col and pd.notna(row.get(info_col)):
                        detail_info = parse_movers_detail_info(row[info_col])

                    # 时间信息
                    time_info = ""
                    if time_col and pd.notna(row.get(time_col)):
                        time_info = f"@{str(row[time_col])}"

                    # 组装显示信息
                    if stock_code:
                        display_text = f"{row[name_col]}({stock_code}){detail_info}{time_info}"
                    else:
                        display_text = f"{row[name_col]}{detail_info}{time_info}"

                    movers_big_buy_list.append(display_text)
    context['movers_big_buy'] = movers_big_buy_list if movers_big_buy_list else ["无"]

    # 有大买盘 - 支持多种文件格式
    heavy_buy_files = []
    for f in all_files_in_day:
        if f.endswith('.csv'):
            # 严格按照fund_data_analyzer.py的文件分类逻辑
            if f == 'movers_有大买盘.csv':
                heavy_buy_files.append(f)

    heavy_buy_files = filter_files_by_time(heavy_buy_files, current_sim_time)
    latest_heavy_buy_file = find_latest_file(heavy_buy_files, current_sim_time)
    if latest_heavy_buy_file:
        df_heavy_buy = load_csv_safe(os.path.join(data_dir, latest_heavy_buy_file))
        if df_heavy_buy is not None and not df_heavy_buy.empty:
            # 适配不同的列名格式
            name_col = next((c for c in ['名称', 'name'] if c in df_heavy_buy.columns), None)
            code_col = next((c for c in ['代码', 'code'] if c in df_heavy_buy.columns), None)
            time_col = next((c for c in ['时间', 'time'] if c in df_heavy_buy.columns), None)
            info_col = next((c for c in ['相关信息', 'info', '相关数据'] if c in df_heavy_buy.columns), None)

            if name_col:
                for _, row in df_heavy_buy.head(50).iterrows():  # 显示前50条
                    stock_code = str(row[code_col]).zfill(6) if code_col and pd.notna(row.get(code_col)) else ""

                    # 解析相关信息
                    detail_info = ""
                    if info_col and pd.notna(row.get(info_col)):
                        detail_info = parse_movers_detail_info(row[info_col])

                    # 时间信息
                    time_info = ""
                    if time_col and pd.notna(row.get(time_col)):
                        time_info = f"@{str(row[time_col])}"

                    # 组装显示信息
                    if stock_code:
                        display_text = f"{row[name_col]}({stock_code}){detail_info}{time_info}"
                    else:
                        display_text = f"{row[name_col]}{detail_info}{time_info}"

                    movers_heavy_buy_list.append(display_text)
    context['movers_heavy_buy'] = movers_heavy_buy_list if movers_heavy_buy_list else ["无"]

    # 6. 组装最终的Prompt
    prompt_string = f"""
你是一位顶级的A股超短线交易分析师（Scout），你的任务不是做最终决策，而是**识别出当前盘面上最有潜力的龙头候选股**。你的分析框架融合了顶级游资的打板、低吸、半路等多种手法，核心是寻找市场合力的方向。

现在是交易日【{context['current_time']}】，请基于以下全局市场信息，找出 1-5 只最值得关注的潜力股，并以JSON格式返回你的分析。

**一、 市场宏观结构 (当前主线/支线):**
{context['market_report']}

**二、 市场情绪指标:**
- **涨停梯队 (最高连板/热门股)**: {', '.join(context['zt_stocks_list'])}
- **炸板/开板个股 (高位兑现/分歧)**: {', '.join(context['zb_stocks_list'])}

**三、 市场资金流向:**
- **个股资金流排名 (前100名)**: {', '.join(context['individual_flow_list'])}

**四、 盘口异动信号 (详细数据):**
- **大笔买入**: {', '.join(context['movers_big_buy'])}
- **有大买盘**: {', '.join(context['movers_heavy_buy'])}

注：异动信号格式说明 - [成交手数|价格|涨跌幅%]@时间

**五、 你的分析任务:**
综合以上所有信息，特别是**主线板块**、**涨停梯队**、**个股资金流**和**盘口异动**四者之间的关联，识别出那些**既在风口上，又有资金持续认可，且具备成为龙头气质**的股票。

重点关注：
1. 当前市场最强的合力方向是什么？(参考主线报告)
2. 这个方向的领涨股是谁？(参考涨停梯队) 资金是否在攻击这个方向？(参考个股资金流排名)
3. 哪些股票出现了大单异动，异动的强度如何？(参考盘口异动的成交量和价格信息)
4. 异动股票是否与主线方向或热门股重合？
5. 综合以上四点，找出 1-5 个最有可能在下一阶段成为市场总龙头的候选者。

**重要格式要求：**
- stock_code 必须是6位数字字符串，如 "000001"、"600000"、"002001" 等
- 不要返回带前缀的代码，如 "sh600000" 或 "sz000001"

请严格按照下面的JSON格式输出你的分析，不要有任何多余的文字。
{{format_instructions}}
"""
    return prompt_string