import pandas as pd
import os
import re
import sqlite3
from datetime import datetime, time, timedelta
from tabulate import tabulate
import logging
import warnings
import sys
from io import StringIO

# --- 导入你原来的核心逻辑函数 ---
# 确保 get_all_capital_flow_east_5_test.py 在同一个目录下
from get_all_capital_flow_east_5_test import (
    get_date_folder,
    convert_to_float,
    format_amount,
    get_stock_type,
    _get_stock_board_map,
    find_breakouts,
    load_and_combine_data_scan,
    BUY_CONDITIONS_SCORE,
    BUY_SCORE_THRESHOLD,
    AM_END_TIME,
    PM_START_TIME,
    MAINLINE_SCORE_THRESHOLD,
    POTENTIAL_SCORE_THRESHOLD,
    _dynamic_theme_discovery,
    generate_master_analysis_report
)


# --- 【新增】AI决策模块导入 ---
from ai_context_provider import get_ai_prompt_context
from ai_decision_maker import get_ai_decision
# --- 【V8.0 新增】AI侦察员模块导入 ---
from ai_scout_context_provider import get_scout_prompt_context
from ai_scout import get_potential_stocks_from_ai


# 【新增】定义开盘和收盘时间
AM_START_TIME = time(9, 15)  # 9:15 开盘时间
PM_END_TIME = time(15, 0)  # 15:00 收盘时间

# 忽略不必要的警告
warnings.filterwarnings("ignore", category=FutureWarning)

# --- 复盘配置 ---
# 数据根目录配置 - 用户可在此修改数据文件夹路径
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'  # 示例：绝对路径，用户可根据实际情况修改
# BASE_DATA_DIR = 'data'  # 备用示例：当数据文件夹在脚本同级目录时使用此设置

BACKTEST_DATE = '2025-07-25'  # <--- 在这里修改你想要复盘的日期！格式：YYYY-MM-DD
# --- 【V7.0 新增】AI决策配置 ---
ENABLE_AI_DECISION = True  # AI决策总开关
AI_TRIGGER_SCORE = 12  # 规则评分达到12分以上才调用AI进行精选
AI_CONFIDENCE_THRESHOLD = 0.7  # AI决策的信心阈值，高于此值才采纳
# --- 【新增】调试配置 ---
DEBUG_AI_RESPONSE = True  # 调试开关：True=打印AI详细回答，False=只显示结果
# -----------------------------
DATA_DIR_FOR_AI = os.path.join(BASE_DATA_DIR, BACKTEST_DATE)  # 为AI提供数据目录

# --- 复盘配置结束 ---
PREVIOUS_SECTOR_DATA_BT = {}  # 存储: {'板块名称': {'inflow': value, 'rank': value}}

# 【V4.0 修改】定义与实时版本一致的龙头锁定分钟数
SECTOR_LEADER_LOCK_MINUTES = 30

# --- 【交易大师心法 V1.0 升级 · 回测版】主线识别与板块强度定义 ---
# 用于存储当前最强的板块列表
STRONG_SECTORS_LIST_BT = []
# 用于存储当前已确认的“主线”板块
MAINLINE_SECTOR_LIST_BT = []
# 用于追踪板块的持续强势表现
# 格式: {'板块名称': 连续进入强势候选的次数}
SECTOR_STRENGTH_TRACKER_BT = {}

# --- 核心判断阈值 (与实时代码保持一致) ---
MAINLINE_CANDIDATE_COUNT = 3
MAINLINE_PERSISTENCE_THRESHOLD = 5
POTENTIAL_CANDIDATE_RANGE_START = 4
POTENTIAL_CANDIDATE_RANGE_END = 15
POTENTIAL_BRANCH_COUNT = 5

# --- 【V4.0 修改】模拟与实时版本一致的全局状态变量
SECTOR_LEADER_LOCK_BT = {}  # V4.0 新增: 龙头锁定, 格式: {'板块名称': {'code': '龙头代码', 'time': sim_time}}
BREAKOUT_STOCKS_LIST_BT = []
PREVIOUS_RANK_DATA_BT = {}
FIRST_SIGNAL_TIMES_BT = {}
# --- 【新增】记录已买过的股票，避免重复询问AI ---
PURCHASED_STOCKS_BT = set()  # 存储已经买过的股票代码

# --- 【新增】主线支线时间序列记录 ---
MAINLINE_TIMELINE_BT = []  # 记录主线支线变化的时间序列


def save_ai_scout_response_to_file(current_time, backtest_date, prompt_string, ai_result):
    """
    【新增】保存AI Scout响应到本地文件
    """
    try:
        # 创建独立的保存目录：Response/ai_scouts/YYYY-MM-DD/
        date_folder = backtest_date
        response_dir = os.path.join("Response", "ai_scouts", date_folder)

        if not os.path.exists(response_dir):
            os.makedirs(response_dir)

        # 生成文件名
        time_str = current_time.strftime('%H-%M-%S')
        response_filename = f"{backtest_date}_{time_str}_scout_report.txt"
        response_filepath = os.path.join(response_dir, response_filename)

        # 格式化潜力股列表
        potential_stocks_text = ""
        potential_stocks_list = ai_result.get('potential_stocks', [])
        if potential_stocks_list:
            for stock in potential_stocks_list:
                potential_stocks_text += f"- {stock.get('stock_name', 'N/A')} ({stock.get('stock_code', 'N/A')}): {stock.get('reasoning', 'N/A')}\n"
        else:
            potential_stocks_text = "未发现潜力股\n"

        # 组装保存内容
        content = f"""====== AI Scout 分析记录 ======
回测日期: {backtest_date}
分析时间: {current_time.strftime('%H:%M:%S')}

====== AI Scout 分析结果 ======
市场总结: {ai_result.get('market_summary', 'N/A')}
潜力股列表:
{potential_stocks_text}

====== 提交给AI Scout的完整信息 ======
{prompt_string}

====== 原始AI Scout响应 ======
{ai_result}
"""

        # 保存到文件
        with open(response_filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        logging.info(f"AI Scout分析响应已保存到: {response_filepath}")

    except Exception as e:
        logging.error(f"保存AI Scout响应文件时发生错误: {e}")


def save_ai_response_to_file(stock_code, stock_name, current_time, backtest_date, prompt_string, ai_result, buy_score, hit_score_reasons):
    """
    【新增】保存AI响应到本地文件，参考SiliconFlow.py的保存机制
    """
    try:
        # 创建保存目录结构：Response/ai_decisions/YYYY-MM-DD/
        date_folder = backtest_date  # 使用回测日期
        response_dir = os.path.join("Response", "ai_decisions", date_folder)

        if not os.path.exists(response_dir):
            os.makedirs(response_dir)

        # 生成文件名：YYYY-MM-DD_HH-MM-SS_股票代码_股票名称_ai_decision.txt
        time_str = current_time.strftime('%H-%M-%S')
        safe_stock_name = re.sub(r'[\\/*?:"<>|]', "", stock_name)  # 清理文件名中的非法字符
        response_filename = f"{backtest_date}_{time_str}_{stock_code}_{safe_stock_name}_ai_decision.txt"
        response_filepath = os.path.join(response_dir, response_filename)

        # 组装保存内容
        content = f"""====== AI决策记录 ======
回测日期: {backtest_date}
决策时间: {current_time.strftime('%H:%M:%S')}
股票信息: {stock_name} ({stock_code})
规则评分: {buy_score}
加分项: {' + '.join(hit_score_reasons)}

====== AI决策结果 ======
决策: {ai_result.get('decision', 'ERROR')}
信心: {ai_result.get('confidence', 0):.2f}
理由: {ai_result.get('reasoning', 'N/A')}
风险: {ai_result.get('risk_assessment', 'N/A')}

====== 提交给AI的完整信息 ======
{prompt_string}

====== 原始AI响应 ======
{ai_result}
"""

        # 保存到文件
        with open(response_filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        logging.info(f"AI决策响应已保存到: {response_filepath}")

    except Exception as e:
        logging.error(f"保存AI响应文件时发生错误: {e}")
        # 不影响主流程，继续执行


# 日志配置类 (无修改)
class BacktestLogger:
    def __init__(self, backtest_date):
        self.backtest_date = backtest_date
        # 将日期格式从 YYYY-MM-DD 转换为 YYYYMMDD 用于文件名
        self.date_for_filename = backtest_date.replace('-', '') if '-' in backtest_date else backtest_date
        self.backtest_folder = os.path.join('backtest', self.date_for_filename)
        os.makedirs(self.backtest_folder, exist_ok=True)
        self.full_day_log_file = os.path.join(self.backtest_folder, f'{self.date_for_filename}_full_day.log')
        self.current_hour_log_file = None
        self.current_hour = None
        self.log_buffer = StringIO()
        self.log_format = '%(asctime)s - %(levelname)s - %(message)s'
        self.setup_full_day_logger()
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        sys.stdout = LogCapture(self, 'INFO')
        sys.stderr = LogCapture(self, 'ERROR')

    def setup_full_day_logger(self):
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        full_day_handler = logging.FileHandler(self.full_day_log_file, encoding='utf-8')
        full_day_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(self.log_format)
        full_day_handler.setFormatter(formatter)
        logging.root.setLevel(logging.INFO)
        logging.root.addHandler(full_day_handler)

    def get_hour_from_time(self, current_time):
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%H:%M:%S').time()
        elif isinstance(current_time, datetime):
            current_time = current_time.time()
        hour = current_time.hour
        minute = current_time.minute
        if hour == 9 and minute >= 30:
            return 9
        elif hour == 10 and minute < 30:
            return 9
        elif hour == 10 and minute >= 30:
            return 10
        elif hour == 11 and minute < 30:
            return 10
        elif hour == 13:
            return 13
        elif hour == 14:
            return 14
        else:
            if minute >= 30:
                return hour
            else:
                return hour - 1 if hour > 0 else 23

    def setup_hourly_logger(self, current_time):
        hour_segment = self.get_hour_from_time(current_time)
        if self.current_hour != hour_segment:
            self.current_hour = hour_segment
            if hour_segment == 9:
                time_range = "0930-1030"
            elif hour_segment == 10:
                time_range = "1030-1130"
            elif hour_segment == 13:
                time_range = "1300-1400"
            elif hour_segment == 14:
                time_range = "1400-1500"
            else:
                time_range = f"{hour_segment:02d}00-{(hour_segment + 1):02d}00"
            self.current_hour_log_file = os.path.join(self.backtest_folder, f'{self.date_for_filename}_{time_range}.log')

    def log_message(self, level, message, current_time=None):
        if current_time:
            self.setup_hourly_logger(current_time)
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"{timestamp} - {level} - {message}\n"
        if level == 'INFO':
            logging.info(message)
        elif level == 'ERROR':
            logging.error(message)
        elif level == 'WARNING':
            logging.warning(message)
        elif level == 'DEBUG':
            logging.debug(message)
        if self.current_hour_log_file:
            try:
                with open(self.current_hour_log_file, 'a', encoding='utf-8') as f:
                    f.write(formatted_message)
            except Exception as e:
                print(f"写入小时日志失败: {e}")

    def restore_stdout(self):
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr


class LogCapture:
    def __init__(self, logger, level):
        self.logger = logger
        self.level = level
        self.original_stream = sys.stdout if level == 'INFO' else sys.stderr

    def write(self, message):
        self.original_stream.write(message)
        self.original_stream.flush()
        if message.strip():
            self.logger.log_message(self.level, message.strip())

    def flush(self):
        self.original_stream.flush()


# 创建全局日志记录器
backtest_logger = BacktestLogger(BACKTEST_DATE)

# --- 【新增】用于在函数间传递详细的板块分析结果 ---
df_analysis_global_bt = pd.DataFrame()


def task_analyze_strong_sectors_backtest(current_industry_df, current_concept_df, all_timestamps, current_sim_time):
    """
    【V6.5 动态量能复盘版】与实时版本完全对齐的板块分析函数。
    (此函数未修改)
    """
    global STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, SECTOR_STRENGTH_TRACKER_BT, PREVIOUS_SECTOR_DATA_BT
    global df_analysis_global_bt

    try:
        # 1. 数据获取和预处理 (逻辑不变)
        all_sectors_list = []
        if current_industry_df is not None and not current_industry_df.empty:
            if '名称' in current_industry_df.columns:
                industry_data = current_industry_df[['名称', '今日涨跌幅', '今日主力净流入-净额']].copy()
                industry_data['type'] = '行业'
                all_sectors_list.append(industry_data)

        if current_concept_df is not None and not current_concept_df.empty:
            rename_map = {}
            if '行业' in current_concept_df.columns: rename_map['行业'] = '名称'
            if '行业-涨跌幅' in current_concept_df.columns: rename_map['行业-涨跌幅'] = '今日涨跌幅'
            if '净额' in current_concept_df.columns: rename_map['净额'] = '今日主力净流入-净额'
            concept_data = current_concept_df.rename(columns=rename_map)
            if '名称' in concept_data.columns and '今日主力净流入-净额' in concept_data.columns:
                concept_data['今日主力净流入-净额'] = concept_data['今日主力净流入-净额'] * 100000000
                concept_data['type'] = '概念'
                all_sectors_list.append(concept_data[['名称', '今日涨跌幅', '今日主力净流入-净额', 'type']])

        if not all_sectors_list:
            STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, df_analysis_global_bt = [], [], pd.DataFrame()
            return

        all_sectors_df = pd.concat(all_sectors_list, ignore_index=True).drop_duplicates(subset=['名称', 'type'])
        all_sectors_df['名称'] = all_sectors_df['名称'].str.strip()
        all_sectors_df['今日涨跌幅'] = all_sectors_df['今日涨跌幅'].apply(convert_to_float)
        all_sectors_df['今日主力净流入-净额'] = all_sectors_df['今日主力净流入-净额'].apply(convert_to_float)
        all_sectors_df.dropna(subset=['名称', '今日涨跌幅', '今日主力净流入-净额'], inplace=True)
        all_sectors_df.sort_values(by='今日主力净流入-净额', ascending=False, inplace=True)
        all_sectors_df.reset_index(drop=True, inplace=True)
        all_sectors_df['rank'] = all_sectors_df.index + 1

        # 2. 计算“资金加速度”和识别“主线挑战者” (逻辑不变)
        all_sectors_df['inflow_delta'] = 0.0
        challenger_sectors = set()
        if PREVIOUS_SECTOR_DATA_BT:
            for index, row in all_sectors_df.iterrows():
                sector_name = row['名称']
                if sector_name in PREVIOUS_SECTOR_DATA_BT:
                    previous_data = PREVIOUS_SECTOR_DATA_BT[sector_name]
                    all_sectors_df.at[index, 'inflow_delta'] = row['今日主力净流入-净额'] - previous_data.get('inflow',
                                                                                                              0)
                    previous_rank = previous_data.get('rank', 999)
                    current_rank = row['rank']
                    if previous_rank > 20 and current_rank <= 10: challenger_sectors.add(sector_name)
                    if 2 <= current_rank <= 5:
                        prev_rank_inflow = \
                        all_sectors_df.loc[all_sectors_df['rank'] == current_rank - 1, '今日主力净流入-净额'].iloc[0]
                        current_gap = prev_rank_inflow - row['今日主力净流入-净额']
                        prev_sector_of_rank_N_minus_1 = [s for s, d in PREVIOUS_SECTOR_DATA_BT.items() if
                                                         d.get('rank') == current_rank - 1]
                        if prev_sector_of_rank_N_minus_1:
                            prev_gap = PREVIOUS_SECTOR_DATA_BT[prev_sector_of_rank_N_minus_1[0]].get('inflow',
                                                                                                     0) - previous_data.get(
                                'inflow', 0)
                            if prev_gap > 0 and current_gap < prev_gap * 0.5: challenger_sectors.add(sector_name)
        all_sectors_df.sort_values(by='inflow_delta', ascending=False, inplace=True)
        all_sectors_df['momentum_rank'] = range(1, len(all_sectors_df) + 1)

        # 3. 【V6.5 核心升级】动态计算“市场量能基准”
        positive_flow_df = all_sectors_df[all_sectors_df['今日主力净流入-净额'] > 0].copy()
        if positive_flow_df.empty:
            STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, df_analysis_global_bt = [], [], pd.DataFrame()
            PREVIOUS_SECTOR_DATA_BT.clear()
            return

        total_positive_inflow = positive_flow_df['今日主力净流入-净额'].sum()
        soul_tier_inflow = total_positive_inflow * 0.10
        core_tier_inflow = total_positive_inflow * 0.05
        active_tier_inflow = total_positive_inflow * 0.02

        # 4. 【V6.5 核心升级】判断“资金断层”
        is_gap_effect = False
        gap_leader_name = None
        if len(positive_flow_df) >= 2:
            top1_inflow, top2_inflow = positive_flow_df['今日主力净流入-净额'].iloc[0], \
            positive_flow_df['今日主力净流入-净额'].iloc[1]
            if top2_inflow > 0 and (top1_inflow / top2_inflow) >= 1.8:
                is_gap_effect, gap_leader_name = True, positive_flow_df.iloc[0]['名称']

        # 5. 【V6.5 核心升级】对所有板块进行多因子评分
        analysis_results = []
        for index, row in all_sectors_df.iterrows():
            sector_name, inflow, rank, reasons, total_score = row['名称'], row['今日主力净流入-净额'], row[
                'rank'], [], 0

            # --- 新评分逻辑开始 ---
            strength_score = 0
            if is_gap_effect and sector_name == gap_leader_name:
                if inflow >= core_tier_inflow:
                    strength_score = 10; reasons.append("主线真龙(10)")
                elif inflow >= active_tier_inflow:
                    strength_score = 5; reasons.append("支线龙头(5)")
                else:
                    strength_score = 2; reasons.append("局部偷袭(2)")
            else:
                capacity_score = 0
                if inflow >= soul_tier_inflow:
                    capacity_score = 5; reasons.append("市场灵魂(5)")
                elif inflow >= core_tier_inflow:
                    capacity_score = 3; reasons.append("核心战场(3)")
                elif inflow >= active_tier_inflow:
                    capacity_score = 1; reasons.append("活跃板块(1)")
                head_position_score = 0
                if rank <= 3 and capacity_score > 0: head_position_score = 2; reasons.append("头部优势(2)")
                strength_score = capacity_score + head_position_score

            if inflow >= active_tier_inflow or strength_score > 0:
                persistence_score = 0
                continuity_count = SECTOR_STRENGTH_TRACKER_BT.get(sector_name, 0)
                if continuity_count >= 30:
                    persistence_score = 3; reasons.append("持续霸榜(3)")
                elif continuity_count >= 15:
                    persistence_score = 1; reasons.append("持续活跃(1)")
                momentum_score = 0
                if row['momentum_rank'] <= 3:
                    momentum_score = 4; reasons.append("动量先锋(4)")
                elif row['momentum_rank'] <= 10:
                    momentum_score = 2; reasons.append("动量跟进(2)")
                challenger_score = 3 if sector_name in challenger_sectors else 0
                if challenger_score > 0: reasons.append("主线挑战者(3)")
                total_score = strength_score + persistence_score + momentum_score + challenger_score
            else:
                momentum_score = 0
                if row['momentum_rank'] <= 3:
                    momentum_score = 4; reasons.append("动量先锋(4)")
                elif row['momentum_rank'] <= 10:
                    momentum_score = 2; reasons.append("动量跟进(2)")
                total_score = momentum_score

            if total_score > 0:
                analysis_results.append({
                    "板块名称": sector_name, "type": row['type'], "总分": total_score, "评分理由": ' + '.join(reasons),
                    "今日涨跌幅": row['今日涨跌幅'], "今日主力净流入-净额": inflow,
                    "持续强势次数": SECTOR_STRENGTH_TRACKER_BT.get(sector_name, 0)
                })

        # 6. 将评分结果保存到全局 (逻辑不变)
        df_analysis_global_bt = pd.DataFrame(analysis_results).sort_values(by="总分",
                                                                           ascending=False) if analysis_results else pd.DataFrame()

        # 7. 根据总分进行分类，并更新全局列表 (逻辑不变)
        if not df_analysis_global_bt.empty:
            mainline_df = df_analysis_global_bt[df_analysis_global_bt['总分'] >= MAINLINE_SCORE_THRESHOLD]
            potential_df = df_analysis_global_bt[(df_analysis_global_bt['总分'] >= POTENTIAL_SCORE_THRESHOLD) & (
                        df_analysis_global_bt['总分'] < MAINLINE_SCORE_THRESHOLD)]
            MAINLINE_SECTOR_LIST_BT = mainline_df['板块名称'].tolist()
            strong_sectors_set = set(MAINLINE_SECTOR_LIST_BT) | set(
                potential_df['板块名称'].tolist()) | challenger_sectors
            STRONG_SECTORS_LIST_BT = list(strong_sectors_set)
        else:
            STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT = [], []

        # 8. 更新持续性追踪器 (逻辑不变)
        current_strong_set = set(STRONG_SECTORS_LIST_BT)
        for sn in current_strong_set: SECTOR_STRENGTH_TRACKER_BT[sn] = SECTOR_STRENGTH_TRACKER_BT.get(sn, 0) + 1
        for sn in list(SECTOR_STRENGTH_TRACKER_BT.keys()):
            if sn not in current_strong_set: SECTOR_STRENGTH_TRACKER_BT[sn] = 0

        # 9. 更新上一分钟的状态数据 (逻辑不变)
        PREVIOUS_SECTOR_DATA_BT.clear()
        for _, row in all_sectors_df.iterrows():
            PREVIOUS_SECTOR_DATA_BT[row['名称']] = {'inflow': row['今日主力净流入-净额'], 'rank': row['rank']}

    except Exception as e:
        import traceback
        logging.error(f"复盘分析强势板块失败: {e}\n{traceback.format_exc()}")
        STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, df_analysis_global_bt = [], [], pd.DataFrame()


def run_breakout_scan_backtest(all_day_df, current_time_for_scan):
    """【复盘版】异动扫描函数 (此函数未修改)"""
    global BREAKOUT_STOCKS_LIST_BT
    try:
        logging.info("--- [复盘] 开始扫描异动拉升股 ---")
        if all_day_df is None or all_day_df.empty:
            BREAKOUT_STOCKS_LIST_BT = []
            return

        if current_time_for_scan <= AM_END_TIME:
            period_name = "上午盘"
            df_period = all_day_df[all_day_df['timestamp'] <= current_time_for_scan]
        else:
            period_name = "下午盘"
            df_period = all_day_df[
                (all_day_df['timestamp'] >= PM_START_TIME) & (all_day_df['timestamp'] <= current_time_for_scan)]

        if df_period.empty:
            BREAKOUT_STOCKS_LIST_BT = []
            return

        _, breakout_codes = find_breakouts(df_period, period_name, all_day_df)
        BREAKOUT_STOCKS_LIST_BT = breakout_codes
        logging.info(f"当前时间点异动股: {BREAKOUT_STOCKS_LIST_BT}")
    except Exception as e:
        logging.error(f"复盘扫描异动股失败: {e}")
        BREAKOUT_STOCKS_LIST_BT = []


def generate_mainline_timeline_report(date_str, timeline_data):
    """
    【新增】生成主线支线时间序列报告 (此函数未修改)
    """
    if not timeline_data:
        return "未记录到主线支线变化数据。"

    # 处理日期显示格式
    if '-' in date_str:
        # 如果是 YYYY-MM-DD 格式，直接使用
        display_date = date_str
    else:
        # 如果是 YYYYMMDD 格式，转换为 YYYY-MM-DD
        display_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

    report_lines = []
    report_lines.append(f"====== {display_date} 主线支线时间序列报告 ======\n")
    for entry in timeline_data:
        report_lines.append(f"【{entry['time']}】")
        report_lines.append(f"  资金主线: {entry['mainline_str']}")
        report_lines.append(f"  潜在支线: {entry['potential_str']}")
        report_lines.append("")
    report_lines.append("=" * 60)
    report_lines.append("【变化统计】")
    mainline_count, potential_count = {}, {}
    for entry in timeline_data:
        for sector in entry['mainline']: mainline_count[sector] = mainline_count.get(sector, 0) + 1
        for sector in entry['potential']: potential_count[sector] = potential_count.get(sector, 0) + 1
    if mainline_count:
        report_lines.append("\n主线板块出现频次:")
        sorted_mainline = sorted(mainline_count.items(), key=lambda x: x[1], reverse=True)
        for sector, count in sorted_mainline:
            percentage = (count / len(timeline_data)) * 100
            report_lines.append(f"  {sector}: {count}次 ({percentage:.1f}%)")
    if potential_count:
        report_lines.append("\n支线板块出现频次:")
        sorted_potential = sorted(potential_count.items(), key=lambda x: x[1], reverse=True)
        for sector, count in sorted_potential:
            percentage = (count / len(timeline_data)) * 100
            report_lines.append(f"  {sector}: {count}次 ({percentage:.1f}%)")
    report_lines.append("\n【主线变化节点】")
    prev_mainline = None
    change_points = []
    for entry in timeline_data:
        current_mainline = set(entry['mainline'])
        if prev_mainline is not None and current_mainline != prev_mainline:
            change_points.append({'time': entry['time'], 'from': list(prev_mainline) if prev_mainline else [],
                                  'to': list(current_mainline)})
        prev_mainline = current_mainline
    if change_points:
        for change in change_points:
            from_str = ', '.join(change['from']) if change['from'] else "无主线"
            to_str = ', '.join(change['to']) if change['to'] else "无主线"
            report_lines.append(f"  {change['time']}: {from_str} → {to_str}")
    else:
        report_lines.append("  全天主线保持稳定，未发生变化")
    report_lines.append("=" * 60)
    return '\n'.join(report_lines)


def save_ai_daily_response_to_file(response_type, current_time, backtest_date, content_data):
    """
    【新增】保存AI全天响应到统一的时间序列文件

    Args:
        response_type: "SCOUT" 或 "TRADER"
        current_time: datetime.time 对象
        backtest_date: 回测日期字符串
        content_data: 包含响应内容的字典
    """
    try:
        # 创建保存目录：Response/ai_daily_timeline/YYYY-MM-DD/
        date_folder = backtest_date
        response_dir = os.path.join("Response", "ai_daily_timeline", date_folder)

        if not os.path.exists(response_dir):
            os.makedirs(response_dir)

        # 生成文件名：YYYY-MM-DD_ai_daily_responses.txt
        response_filename = f"{backtest_date}_ai_daily_responses.txt"
        response_filepath = os.path.join(response_dir, response_filename)

        # 根据响应类型组织内容
        if response_type == "SCOUT":
            # AI Scout响应格式
            potential_stocks_text = ""
            potential_stocks_list = content_data.get('ai_result', {}).get('potential_stocks', [])
            if potential_stocks_list:
                for stock in potential_stocks_list:
                    potential_stocks_text += f"    - {stock.get('stock_name', 'N/A')} ({stock.get('stock_code', 'N/A')}): {stock.get('reasoning', 'N/A')}\n"
            else:
                potential_stocks_text = "    未发现潜力股\n"

            entry_content = f"""
========== [{current_time.strftime('%H:%M:%S')}] AI Scout 分析 ==========
市场总结: {content_data.get('ai_result', {}).get('market_summary', 'N/A')}
潜力股列表:
{potential_stocks_text}"""

        elif response_type == "TRADER":
            # AI Trader响应格式
            entry_content = f"""
---------- [{current_time.strftime('%H:%M:%S')}] AI Trader 决策 ----------
股票信息: {content_data.get('stock_name', 'N/A')} ({content_data.get('stock_code', 'N/A')})
规则评分: {content_data.get('buy_score', 0)} ({content_data.get('hit_score_reasons', 'N/A')})
AI决策: {content_data.get('ai_result', {}).get('decision', 'N/A')} (信心: {content_data.get('ai_result', {}).get('confidence', 0):.2f})
理由: {content_data.get('ai_result', {}).get('reasoning', 'N/A')}
风险: {content_data.get('ai_result', {}).get('risk_assessment', 'N/A')}"""

        else:
            entry_content = f"[{current_time.strftime('%H:%M:%S')}] 未知响应类型: {response_type}"

        # 追加写入文件（保持时间顺序）
        with open(response_filepath, 'a', encoding='utf-8') as f:
            f.write(entry_content + "\n")

        logging.info(f"AI {response_type} 响应已追加到日度时间序列文件: {response_filepath}")

    except Exception as e:
        logging.error(f"保存AI日度响应文件时发生错误: {e}")
        # 不影响主流程，继续执行


def run_backtest(date_str):
    """
    【V8.1 AI Scout调试增强版】主复盘函数
    """
    global PREVIOUS_RANK_DATA_BT, FIRST_SIGNAL_TIMES_BT
    global STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, BREAKOUT_STOCKS_LIST_BT, SECTOR_LEADER_LOCK_BT
    global MAINLINE_TIMELINE_BT
    global df_analysis_global_bt, PURCHASED_STOCKS_BT

    print(f"--- 开始对日期 {date_str} 进行盘后复盘 (V8.1 AI Scout调试增强版) ---")
    logging.info(f"--- 开始对日期 {date_str} 进行盘后复盘 (V8.1 AI Scout调试增强版) ---")
    backtest_logger.log_message('INFO', f"开始对日期 {date_str} 进行盘后复盘 (V8.1 AI Scout调试增强版)")

    # 检查AI数据目录是否存在
    if not os.path.isdir(DATA_DIR_FOR_AI):
        print(f"错误: 未找到日期 {DATA_DIR_FOR_AI} 的数据文件夹！AI模块将无法获取上下文。")
        return

    # 1. 准备数据文件列表 (无修改)
    rank_files = sorted(
        [f for f in os.listdir(DATA_DIR_FOR_AI) if f.startswith('fund_flow_rank_') and f.endswith('.csv')])
    industry_sector_files = sorted([f for f in os.listdir(DATA_DIR_FOR_AI) if
                                    'sector_fund_flow' in f and f.endswith('.csv') and not f.startswith(
                                        'concept_fund_flow') and not f.startswith('sector_summary')])
    concept_sector_files = sorted(
        [f for f in os.listdir(DATA_DIR_FOR_AI) if f.startswith('concept_fund_flow_') and f.endswith('.csv')])
    sector_summary_files = [f for f in os.listdir(DATA_DIR_FOR_AI) if
                            f.startswith('sector_summary_') and f.endswith('.csv')]
    acceleration_files = sorted(
        [f for f in os.listdir(DATA_DIR_FOR_AI) if f.startswith('acceleration_signals_') and f.endswith('.csv')])
    big_deal_files = sorted(
        [f for f in os.listdir(DATA_DIR_FOR_AI) if f.startswith('big_deal_') and f.endswith('.csv')])
    rank_change_files = sorted(
        [f for f in os.listdir(DATA_DIR_FOR_AI) if f.startswith('rank_change_') and f.endswith('.csv')])

    if not rank_files:
        print("错误: 未找到任何 fund_flow_rank 数据文件！")
        return

    all_day_rank_df = load_and_combine_data_scan(DATA_DIR_FOR_AI)
    stock_board_map_df = _get_stock_board_map()
    all_timestamps = [datetime.strptime(f.split('_')[-1].replace('.csv', ''), '%H%M%S').time() for f in rank_files]

    all_buy_signals = []
    processed_buy_signals = set()
    master_report = "市场分析报告尚未生成"  # 初始化报告变量
    previous_mainline = None
    previous_potential = None
    MAINLINE_TIMELINE_BT = []

    # 2. 循环模拟每个时间点
    for i, rank_file in enumerate(rank_files):
        timestamp_str = rank_file.split('_')[-1].replace('.csv', '')
        current_sim_time = datetime.strptime(timestamp_str, '%H%M%S').time()

        if current_sim_time < AM_START_TIME or current_sim_time > PM_END_TIME:
            continue

        expired_sectors = [sector for sector, lock_info in SECTOR_LEADER_LOCK_BT.items() if
                           datetime.combine(datetime.min, current_sim_time) - datetime.combine(datetime.min, lock_info[
                               'time']) > timedelta(minutes=SECTOR_LEADER_LOCK_MINUTES)]
        for sector in expired_sectors:
            if sector in SECTOR_LEADER_LOCK_BT:
                del SECTOR_LEADER_LOCK_BT[sector]

        backtest_logger.setup_hourly_logger(current_sim_time)
        print(f"\n--- 模拟时间点: {current_sim_time} ({i + 1}/{len(rank_files)}) ---")

        try:
            current_rank_df = pd.read_csv(os.path.join(DATA_DIR_FOR_AI, rank_file), encoding='utf-8-sig')
            current_rank_df['代码'] = current_rank_df['代码'].astype(str).str.zfill(6)
            current_rank_df['今日涨跌幅'] = pd.to_numeric(current_rank_df['今日涨跌幅'], errors='coerce').fillna(0)
        except Exception as e:
            logging.error(f"读取或处理文件 {rank_file} 失败: {e}")
            continue

        def find_latest_file(file_list, current_time):
            relevant_file = None
            for f in sorted(file_list):
                try:
                    match = re.search(r'(\d{8})_(\d{6})', f) or re.search(r'_(\d{6})\.csv', f)
                    if not match: continue
                    f_ts_str = match.groups()[-1]
                    f_ts = datetime.strptime(f_ts_str, '%H%M%S').time()
                    if f_ts <= current_time:
                        relevant_file = f
                    else:
                        break
                except (ValueError, IndexError):
                    continue
            return relevant_file

        latest_industry_file = find_latest_file(industry_sector_files, current_sim_time)
        latest_concept_file = find_latest_file(concept_sector_files, current_sim_time)
        try:
            current_industry_df = pd.read_csv(os.path.join(DATA_DIR_FOR_AI, latest_industry_file), encoding='utf-8-sig',
                                              on_bad_lines='skip') if latest_industry_file else pd.DataFrame()
        except Exception:
            current_industry_df = pd.DataFrame()
        try:
            current_concept_df = pd.read_csv(os.path.join(DATA_DIR_FOR_AI, latest_concept_file), encoding='utf-8-sig',
                                             on_bad_lines='skip') if latest_concept_file else pd.DataFrame()
        except Exception:
            current_concept_df = pd.DataFrame()

        task_analyze_strong_sectors_backtest(current_industry_df, current_concept_df, all_timestamps, current_sim_time)
        run_breakout_scan_backtest(all_day_rank_df, current_sim_time)

        current_mainline = set(MAINLINE_SECTOR_LIST_BT)
        current_potential = set([s for s in STRONG_SECTORS_LIST_BT if s not in current_mainline])

        if previous_mainline is None or (
                current_mainline != previous_mainline or current_potential != previous_potential):
            print("\n" + "=" * 25 + f" 市场结构分析报告 @ {current_sim_time.strftime('%H:%M')} " + "=" * 25)
            master_report = generate_master_analysis_report(df_analysis_global_bt, MAINLINE_SCORE_THRESHOLD,
                                                            POTENTIAL_SCORE_THRESHOLD)
            print(master_report)
            print("=" * 85 + "\n")
            previous_mainline = current_mainline.copy()
            previous_potential = current_potential.copy()

        timeline_entry = {'time': current_sim_time.strftime('%H:%M:%S'), 'mainline': list(current_mainline),
                          'potential': list(current_potential),
                          'mainline_str': ', '.join(current_mainline) if current_mainline else "主线模糊",
                          'potential_str': ', '.join(current_potential) if current_potential else "无"}
        MAINLINE_TIMELINE_BT.append(timeline_entry)

        # --- 【V8.1 核心升级】两阶段AI决策流程 (带Scout调试) ---
        print("--- [AI Scout] 开始分析当前盘面，寻找潜力龙头... ---")
        scout_prompt = get_scout_prompt_context(current_sim_time, DATA_DIR_FOR_AI, master_report)

        # 【新增】当调试开启时，打印Scout的Prompt
        if DEBUG_AI_RESPONSE:
            print("  -> 提交给 AI Scout 的决策信息:")
            print(f"     {scout_prompt}")
            print("  -> 等待 AI Scout 分析中...")

        scout_result = get_potential_stocks_from_ai(scout_prompt)

        # 【新增】保存Scout的响应
        save_ai_scout_response_to_file(
            current_time=current_sim_time,
            backtest_date=BACKTEST_DATE,
            prompt_string=scout_prompt,
            ai_result=scout_result
        )

        # 【新增】保存Scout响应到日度时间序列文件
        save_ai_daily_response_to_file(
            response_type="SCOUT",
            current_time=current_sim_time,
            backtest_date=BACKTEST_DATE,
            content_data={'ai_result': scout_result}
        )

        # 【新增】当调试开启时，打印Scout的详细响应
        if DEBUG_AI_RESPONSE:
            print(f"  -> AI Scout 盘面总结: {scout_result.get('market_summary', 'N/A')}")
            potential_stocks_details = scout_result.get('potential_stocks', [])
            if potential_stocks_details:
                print("  -> AI Scout 发现潜力股:")
                for stock in potential_stocks_details:
                    stock_code = str(stock.get('stock_code', '000000')).zfill(6)
                    print(f"     - {stock.get('stock_name')} ({stock_code}): {stock.get('reasoning')}")
            else:
                print("  -> AI Scout 未发现潜力股。")
        else:
            print(f"--- [AI Scout] 盘面总结: {scout_result.get('market_summary', '无')} ---")

        potential_stocks = scout_result.get('potential_stocks', [])

        # 【新增】验证并修正股票代码格式
        validated_stocks = []
        for stock in potential_stocks:
            if isinstance(stock, dict) and 'stock_code' in stock and 'stock_name' in stock:
                stock_code = str(stock['stock_code']).zfill(6)
                stock['stock_code'] = stock_code  # 确保6位格式
                validated_stocks.append(stock)
            else:
                print(f"  -> 警告: AI Scout返回的股票数据格式异常，跳过: {stock}")

        potential_stocks = validated_stocks

        if not potential_stocks:
            print("--- [AI Scout] 未发现值得关注的潜力股，进入下一时间点。 ---")
            continue

        print(f"--- [AI Scout] 发现 {len(potential_stocks)} 只潜力股，交由AI Trader进行决策... ---")
        buy_details_this_step = []

        for stock_candidate in potential_stocks:
            stock_code = str(stock_candidate['stock_code']).zfill(6)
            stock_name = stock_candidate['stock_name']
            scout_reasoning = stock_candidate.get('reasoning', '无')

            print(f"\n  -> [AI Trader] 正在评估: {stock_name} ({stock_code}) | Scout理由: {scout_reasoning}")

            # 验证股票代码格式
            if not stock_code.isdigit() or len(stock_code) != 6:
                print(f"  -> 警告: 股票代码格式异常 ({stock_code})，跳过评估。")
                continue

            row_series_list = current_rank_df[current_rank_df['代码'] == stock_code]
            if row_series_list.empty:
                print(f"  -> 警告: 在资金流排名文件中未找到 {stock_name} ({stock_code}) 的详细数据，跳过评估。")
                continue
            row = row_series_list.iloc[0]

            if stock_code in PURCHASED_STOCKS_BT:
                print(f"  -> {stock_name} 已买过，跳过AI决策")
                continue

            main_net_abs = convert_to_float(row.get('今日主力净流入-净额', 0))
            main_ratio = convert_to_float(row.get('今日主力净流入-净占比', 0))
            change_percent = convert_to_float(row.get('今日涨跌幅', 0))
            price = convert_to_float(row.get('最新价', 0))
            stock_concepts = stock_board_map_df[stock_board_map_df['代码'] == stock_code]['概念名称'].tolist()
            related_sectors = [c for c in stock_concepts if c in STRONG_SECTORS_LIST_BT]
            is_mainline = any(s in MAINLINE_SECTOR_LIST_BT for s in related_sectors)

            # 规则评分作为参考
            buy_score = 0
            hit_score_reasons = []
            if is_mainline:
                buy_score += BUY_CONDITIONS_SCORE['mainline_sector_bonus']
                hit_score_reasons.append(f"主线板块({BUY_CONDITIONS_SCORE['mainline_sector_bonus']}分)")
            elif related_sectors:
                buy_score += BUY_CONDITIONS_SCORE['sector_bonus']
                hit_score_reasons.append(f"强势板块({BUY_CONDITIONS_SCORE['sector_bonus']}分)")
            if stock_code in BREAKOUT_STOCKS_LIST_BT:
                buy_score += BUY_CONDITIONS_SCORE['breakout_bonus']
                hit_score_reasons.append(f"异动拉升({BUY_CONDITIONS_SCORE['breakout_bonus']}分)")
            if (main_net_abs > 50000000 and main_ratio > 15):
                buy_score += BUY_CONDITIONS_SCORE['fund_advantage']
                hit_score_reasons.append(f"资金优势({BUY_CONDITIONS_SCORE['fund_advantage']}分)")
            if change_percent > 7:
                buy_score += BUY_CONDITIONS_SCORE.get('price_breakout_strong', 3)
                hit_score_reasons.append(f"强势涨幅(>{BUY_CONDITIONS_SCORE.get('price_breakout_strong', 3)}分)")

            # AI Trader 最终决策
            prompt_string = get_ai_prompt_context(stock_code, stock_name, current_sim_time, DATA_DIR_FOR_AI,
                                                  master_report, row, buy_score, hit_score_reasons, related_sectors,
                                                  is_mainline)

            if DEBUG_AI_RESPONSE:
                print(f"  -> 提交给AI Trader的决策信息:")
                print(f"     {prompt_string}")
                print(f"  -> 等待AI Trader决策中...")

            ai_result = get_ai_decision(prompt_string)

            if DEBUG_AI_RESPONSE:
                print(
                    f"  -> [AI Trader] 决策: {ai_result.get('decision', 'ERROR')} (信心: {ai_result.get('confidence', 0):.2f}), 理由: {ai_result.get('reasoning', '')}")

            save_ai_response_to_file(stock_code, stock_name, current_sim_time, BACKTEST_DATE, prompt_string, ai_result,
                                     buy_score, hit_score_reasons)

            # 【新增】保存Trader响应到日度时间序列文件
            save_ai_daily_response_to_file(
                response_type="TRADER",
                current_time=current_sim_time,
                backtest_date=BACKTEST_DATE,
                content_data={
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'buy_score': buy_score,
                    'hit_score_reasons': ' + '.join(hit_score_reasons),
                    'ai_result': ai_result
                }
            )

            if ai_result.get('decision') == 'BUY' and ai_result.get('confidence', 0) >= AI_CONFIDENCE_THRESHOLD:
                if stock_code not in processed_buy_signals:
                    PURCHASED_STOCKS_BT.add(stock_code)
                    processed_buy_signals.add(stock_code)
                    for sector in related_sectors:
                        if sector in MAINLINE_SECTOR_LIST_BT and sector not in SECTOR_LEADER_LOCK_BT:
                            SECTOR_LEADER_LOCK_BT[sector] = {'code': stock_code, 'time': current_sim_time}

                    signal_source_tag = "【AI双重决策】"
                    score_reason = f"Scout:{scout_reasoning} | Trader:{ai_result['reasoning']} (信心:{ai_result['confidence']:.2f})"
                    detail_message = f"{signal_source_tag}: {stock_code} ({stock_name}): [{score_reason}] - {current_sim_time.strftime('%H:%M:%S')}"
                    buy_details_this_step.append(detail_message)

                    signal_info = {
                        'Time': f"{date_str} {current_sim_time.strftime('%H:%M:%S')}", 'Stock_Code': stock_code,
                        'Stock_Name': stock_name, 'Price': price, 'Change_Percent': change_percent,
                        'Main_Flow': main_net_abs, 'Score': buy_score, 'Score_Reasons': ' + '.join(hit_score_reasons),
                        'Mainline_Sectors': ', '.join([s for s in related_sectors if s in MAINLINE_SECTOR_LIST_BT]),
                        'Potential_Sectors': ', '.join([s for s in related_sectors if
                                                        s in STRONG_SECTORS_LIST_BT and s not in MAINLINE_SECTOR_LIST_BT]),
                        'AI_Decision': ai_result.get('decision'), 'AI_Confidence': ai_result.get('confidence'),
                        'AI_Reasoning': ai_result.get('reasoning'), 'AI_Risk': ai_result.get('risk_assessment')
                    }
                    all_buy_signals.append(signal_info)
            else:
                print(f"  -> [AI Trader] 否决或信心不足，放弃对 {stock_name} 的买入。")

        if buy_details_this_step:
            print("\n--- 本时段龙头信号详情 ---")
            for detail in buy_details_this_step:
                print(detail)
                backtest_logger.log_message('INFO', detail, current_sim_time)

        if not current_rank_df.empty:
            print(f"\n--- 资金流向排行榜前20名 ---")
            display_df = current_rank_df.head(20)[
                ['代码', '名称', '今日主力净流入-净额', '今日主力净流入-净占比', '今日超大单净流入-净额',
                 '今日超大单净流入-净占比', '今日涨跌幅', '最新价']].copy()
            display_df['今日主力净流入-净额'] = display_df['今日主力净流入-净额'].apply(
                lambda x: format_amount(float(x)) if pd.notna(x) else '0.00')
            display_df['今日超大单净流入-净额'] = display_df['今日超大单净流入-净额'].apply(
                lambda x: format_amount(float(x)) if pd.notna(x) else '0.00')
            display_df['今日主力净流入-净占比'] = display_df['今日主力净流入-净占比'].apply(
                lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%')
            display_df['今日超大单净流入-净占比'] = display_df['今日超大单净流入-净占比'].apply(
                lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%')
            display_df['今日涨跌幅'] = display_df['今日涨跌幅'].apply(
                lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%')
            display_df['最新价'] = display_df['最新价'].apply(lambda x: f"{float(x):.2f}" if pd.notna(x) else '0.00')
            print(tabulate(display_df, headers='keys', tablefmt='psql', showindex=False))
            print(f"--- 排行榜结束 ---\n")

    # 3. 生成最终总结报告
    print("\n" + "=" * 60 + " 复盘总结报告 " + "=" * 60)
    analyzed_files = [f for f in rank_files if AM_START_TIME <= datetime.strptime(f.split('_')[-1].replace('.csv', ''),
                                                                                  '%H%M%S').time() <= PM_END_TIME]
    if '-' in date_str:
        display_date = date_str
    else:
        display_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"

    final_summary_stats = f"""复盘日期: {display_date}
            分析时间范围: {AM_START_TIME.strftime('%H:%M')}开盘至{PM_END_TIME.strftime('%H:%M')}收盘
            总数据时间点: {len(rank_files)} 个
            实际分析时间点: {len(analyzed_files)} 个
            买入信号总数: {len(all_buy_signals)} 个
            """
    print(final_summary_stats)
    print("=" * 140)
    backtest_logger.log_message('INFO', "复盘总结报告:")
    backtest_logger.log_message('INFO', final_summary_stats)

    print("\n" + "=" * 60 + " 主线支线时间序列分析 " + "=" * 60)
    timeline_report = generate_mainline_timeline_report(date_str, MAINLINE_TIMELINE_BT)
    print(timeline_report)
    date_for_filename = date_str.replace('-', '') if '-' in date_str else date_str
    timeline_file_path = os.path.join(DATA_DIR_FOR_AI, f'mainline_timeline_{date_for_filename}.txt')
    with open(timeline_file_path, 'w', encoding='utf-8') as f:
        f.write(timeline_report)
    print(f"\n主线支线时间序列报告已保存至: {timeline_file_path}")

    summary_file_path = os.path.join(DATA_DIR_FOR_AI, f'backtest_buy_summary_{date_for_filename}.txt')

    if not all_buy_signals:
        print("\n--- 复盘完成，未触发任何买入信号 ---")
        final_report_text = f"====== {display_date} 复盘统计报告 ======\n\n"
        final_report_text += final_summary_stats
        final_report_text += "\n\n结论: 当日市场未出现符合【AI双重决策】买入条件的龙头股信号。"
        with open(summary_file_path, 'w', encoding='utf-8') as f:
            f.write(final_report_text)
        print(f"复盘统计报告已保存至: {summary_file_path}")
        return

    all_buy_signals.sort(key=lambda x: x['Time'])
    report_title = f"====== {display_date} 买入信号【AI双重决策复盘】报告 ======\n\n"
    report_body = ""

    for signal in all_buy_signals:
        mainline_info = signal.get('Mainline_Sectors') or "无"
        potential_info = signal.get('Potential_Sectors') or "无"
        signal_block = f"""--------------------------------------------------
触发时间: {signal['Time']}
股票信息: {signal['Stock_Name']} ({signal['Stock_Code']})
触发价格: {signal['Price']:.2f} (涨跌幅: {signal['Change_Percent']:.2f}%)
主力净流入: {format_amount(signal['Main_Flow'])}
规则评分: {signal['Score']} ({signal['Score_Reasons']})
所属主线: {mainline_info}
所属支线: {potential_info}

【AI 决策】
- 决策: {signal.get('AI_Decision', 'N/A')} (信心: {signal.get('AI_Confidence', 0):.2f})
- 理由: {signal.get('AI_Reasoning', 'N/A')}
- 风险: {signal.get('AI_Risk', 'N/A')}
"""
        report_body += signal_block

    final_report_text = report_title + final_summary_stats + "\n\n买入信号详情:\n" + report_body
    with open(summary_file_path, 'w', encoding='utf-8') as f:
        f.write(final_report_text)

    print("\n--- 复盘完成 ---")
    print(final_report_text)
    print(f"详细复盘报告已保存至: {summary_file_path}")
    backtest_logger.restore_stdout()


if __name__ == "__main__":
    # 确保get_all_capital_flow_east_5_test.py中的函数和变量能被正确导入
    # 确保ai_context_provider.py和ai_decision_maker.py在同一目录下
    try:
        run_backtest(BACKTEST_DATE)
    finally:
        backtest_logger.restore_stdout()