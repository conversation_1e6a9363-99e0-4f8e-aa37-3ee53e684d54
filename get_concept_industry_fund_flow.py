#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
概念、行业资金流获取工具
根据 akshare 接口获取概念、行业的资金流数据，自动获取前10名资金流入的概念/行业，
然后获取对应的个股资金流数据并保存。

作者: AI Assistant
创建时间: 2025-07-28
依赖: akshare, pandas, schedule, time
"""

import akshare as ak
import pandas as pd
import schedule
import time
import os
import random
import logging
from datetime import datetime
from tabulate import tabulate
import warnings

# ================================= 配置区域 =================================
# 数据保存基础路径配置
BASE_DATA_PATH = r"D:\dev\mootdx\adata\Gemini\fund_data"

# 其他配置
DELAY_MIN = 5  # 最小延时秒数
DELAY_MAX = 10  # 最大延时秒数
ERROR_DELAY_MIN = 8  # 出错时最小延时秒数
ERROR_DELAY_MAX = 12  # 出错时最大延时秒数
SCHEDULE_MINUTES = 4  # 定时获取间隔（分钟）
TOP_N = 5  # 获取前N名的概念/行业（修改为5个）

# 缓存配置
USE_CACHE = True  # 是否使用缓存，True=使用缓存，False=重新获取板块名称
CACHE_FILE_CONCEPT = "concept_names_cache.csv"  # 概念板块名称缓存文件
CACHE_FILE_INDUSTRY = "industry_names_cache.csv"  # 行业板块名称缓存文件

# 交易时间配置
TRADING_TIMES = [
    (9, 15, 11, 30),  # 上午交易时间 09:15-11:30
    (13, 0, 15, 0)    # 下午交易时间 13:00-15:00
]
# =========================================================================

# 忽略pandas和akshare的警告
warnings.filterwarnings("ignore", category=FutureWarning)

def setup_logging():
    """设置日志配置"""
    log_dir = os.path.join(BASE_DATA_PATH, "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, f'fund_flow_{datetime.now().strftime("%Y-%m-%d")}.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def get_concept_names_cache():
    """获取概念板块名称（带缓存）"""
    cache_path = os.path.join(get_date_folder(), CACHE_FILE_CONCEPT)

    # 如果使用缓存且缓存文件存在
    if USE_CACHE and os.path.exists(cache_path):
        try:
            concept_names_df = pd.read_csv(cache_path, encoding='utf-8-sig')
            logging.info(f"从缓存加载概念板块名称，共 {len(concept_names_df)} 个概念")
            return concept_names_df
        except Exception as e:
            logging.warning(f"读取概念板块名称缓存失败: {e}，将重新获取")

    # 重新获取概念板块名称
    try:
        logging.info("正在获取概念板块名称...")
        concept_names_df = ak.stock_board_concept_name_em()

        if not concept_names_df.empty:
            # 保存到缓存
            concept_names_df.to_csv(cache_path, index=False, encoding='utf-8-sig')
            logging.info(f"成功获取并缓存概念板块名称，共 {len(concept_names_df)} 个概念")
            return concept_names_df
        else:
            logging.error("获取概念板块名称失败：返回空数据")
            return pd.DataFrame()
    except Exception as e:
        logging.error(f"获取概念板块名称失败: {e}")
        return pd.DataFrame()

def get_industry_names_cache():
    """获取行业板块名称（带缓存）"""
    cache_path = os.path.join(get_date_folder(), CACHE_FILE_INDUSTRY)

    # 如果使用缓存且缓存文件存在
    if USE_CACHE and os.path.exists(cache_path):
        try:
            industry_names_df = pd.read_csv(cache_path, encoding='utf-8-sig')
            logging.info(f"从缓存加载行业板块名称，共 {len(industry_names_df)} 个行业")
            return industry_names_df
        except Exception as e:
            logging.warning(f"读取行业板块名称缓存失败: {e}，将重新获取")

    # 重新获取行业板块名称
    try:
        logging.info("正在获取行业板块名称...")
        industry_names_df = ak.stock_board_industry_name_em()

        if not industry_names_df.empty:
            # 保存到缓存
            industry_names_df.to_csv(cache_path, index=False, encoding='utf-8-sig')
            logging.info(f"成功获取并缓存行业板块名称，共 {len(industry_names_df)} 个行业")
            return industry_names_df
        else:
            logging.error("获取行业板块名称失败：返回空数据")
            return pd.DataFrame()
    except Exception as e:
        logging.error(f"获取行业板块名称失败: {e}")
        return pd.DataFrame()

def get_date_folder():
    """获取按日期命名的文件夹路径（如 D:/dev/mootdx/test/tpdog/gainian/data/2025-07-28）"""
    date_str = datetime.now().strftime('%Y-%m-%d')
    folder = os.path.join(BASE_DATA_PATH, date_str)
    os.makedirs(folder, exist_ok=True)
    return folder

def format_amount(amount):
    """将金额转换为友好格式（如 7000万、1亿）"""
    try:
        amount = float(amount)
        if abs(amount) >= 100000000:
            return f"{amount / 100000000:.2f}亿"
        elif abs(amount) >= 10000:
            return f"{amount / 10000:.2f}万"
        return f"{amount:.2f}"
    except:
        return str(amount)

def is_trading_time():
    """判断当前时间是否在交易时间内"""
    now = datetime.now()
    current_time = now.time()
    
    # 检查是否是周末
    if now.weekday() >= 5:  # 周六=5, 周日=6
        return False
    
    # 检查是否在交易时间内
    for start_hour, start_min, end_hour, end_min in TRADING_TIMES:
        start_time = current_time.replace(hour=start_hour, minute=start_min, second=0, microsecond=0)
        end_time = current_time.replace(hour=end_hour, minute=end_min, second=0, microsecond=0)
        
        if start_time <= current_time <= end_time:
            return True
    
    return False

def get_next_trading_time():
    """获取下一个交易时间段的开始时间"""
    now = datetime.now()
    current_time = now.time()
    
    # 检查今天的交易时间
    for start_hour, start_min, end_hour, end_min in TRADING_TIMES:
        start_time = current_time.replace(hour=start_hour, minute=start_min, second=0, microsecond=0)
        if current_time < start_time:
            return f"{start_hour:02d}:{start_min:02d}"
    
    # 如果今天的交易时间都过了，返回明天第一个交易时间
    next_start = TRADING_TIMES[0]
    return f"明天 {next_start[0]:02d}:{next_start[1]:02d}"

def random_delay():
    """随机延时3-6秒"""
    delay = random.uniform(DELAY_MIN, DELAY_MAX)
    logging.info(f"随机延时 {delay:.2f} 秒")
    time.sleep(delay)

def error_delay():
    """出错时随机延时8-12秒"""
    delay = random.uniform(ERROR_DELAY_MIN, ERROR_DELAY_MAX)
    logging.info(f"出错重试延时 {delay:.2f} 秒")
    time.sleep(delay)

def safe_clean_filename(filename):
    """清理文件名中的非法字符"""
    import re
    return re.sub(r'[\\/*?:"<>|]', "_", filename)

def get_industry_fund_flow():
    """获取行业资金流排名数据"""
    try:
        logging.info("正在获取行业资金流数据...")

        # 添加初始随机延时
        random_delay()

        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                industry_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")

                if industry_df is not None and not industry_df.empty:
                    logging.info(f"成功获取行业资金流数据，共 {len(industry_df)} 条记录")
                    return industry_df
                else:
                    logging.warning(f"第 {attempt + 1} 次尝试：获取的行业资金流数据为空")

            except Exception as e:
                logging.warning(f"第 {attempt + 1} 次尝试获取行业资金流失败: {e}")
                if attempt < max_retries - 1:
                    # 使用更长的错误延时（5-8秒）
                    error_delay()
                    logging.info(f"错误延时后进行第 {attempt + 2} 次重试...")
                else:
                    logging.error(f"行业资金流数据获取完全失败，已重试 {max_retries} 次")

        return pd.DataFrame()

    except Exception as e:
        logging.error(f"获取行业资金流数据发生未知错误: {e}")
        return pd.DataFrame()

def get_concept_fund_flow():
    """获取概念资金流数据"""
    try:
        logging.info("正在获取概念资金流数据...")

        # 添加初始随机延时
        random_delay()

        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 使用东方财富的概念板块资金流排名接口
                concept_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="概念资金流")

                if concept_df is not None and not concept_df.empty:
                    # 检查必要列是否存在
                    required_cols = ['名称', '今日涨跌幅', '今日主力净流入-净额']
                    if all(col in concept_df.columns for col in required_cols):
                        concept_df_std = concept_df[required_cols].copy()
                        concept_df_std['type'] = '概念'
                        logging.info(f"✅ 成功获取概念资金流数据，共 {len(concept_df_std)} 条记录")
                        return concept_df_std
                    else:
                        logging.warning(f"概念资金流数据格式异常，缺少必要列: {list(concept_df.columns)}")
                        return pd.DataFrame()
                else:
                    logging.warning(f"第 {attempt + 1} 次尝试：获取的概念资金流数据为空")

            except Exception as e:
                logging.warning(f"第 {attempt + 1} 次尝试获取概念资金流失败: {e}")
                if attempt < max_retries - 1:
                    # 使用更长的错误延时（5-8秒）
                    error_delay()
                    logging.info(f"错误延时后进行第 {attempt + 2} 次重试...")
                else:
                    logging.error(f"概念资金流数据获取完全失败，已重试 {max_retries} 次")

        return pd.DataFrame()

    except Exception as e:
        logging.error(f"获取概念资金流数据发生未知错误: {e}")
        return pd.DataFrame()

def get_zt_pool_data(date=None):
    """获取涨停股池数据"""
    try:
        if date is None:
            date = datetime.now().strftime('%Y%m%d')

        logging.info(f"正在获取涨停股池数据（日期: {date}）...")

        # 添加初始随机延时
        random_delay()

        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                zt_pool_df = ak.stock_zt_pool_em(date=date)

                if zt_pool_df is not None and not zt_pool_df.empty:
                    logging.info(f"✅ 成功获取涨停股池数据，共 {len(zt_pool_df)} 只股票")
                    return zt_pool_df
                else:
                    logging.warning(f"第 {attempt + 1} 次尝试：获取的涨停股池数据为空")

            except Exception as e:
                logging.warning(f"第 {attempt + 1} 次尝试获取涨停股池失败: {e}")
                if attempt < max_retries - 1:
                    error_delay()
                    logging.info(f"错误延时后进行第 {attempt + 2} 次重试...")
                else:
                    logging.error(f"涨停股池数据获取完全失败，已重试 {max_retries} 次")

        return pd.DataFrame()

    except Exception as e:
        logging.error(f"获取涨停股池数据发生未知错误: {e}")
        return pd.DataFrame()

def get_dt_pool_data(date=None):
    """获取跌停股池数据"""
    try:
        if date is None:
            date = datetime.now().strftime('%Y%m%d')

        logging.info(f"正在获取跌停股池数据（日期: {date}）...")

        # 添加初始随机延时
        random_delay()

        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                dt_pool_df = ak.stock_zt_pool_dtgc_em(date=date)

                if dt_pool_df is not None and not dt_pool_df.empty:
                    logging.info(f"✅ 成功获取跌停股池数据，共 {len(dt_pool_df)} 只股票")
                    return dt_pool_df
                else:
                    logging.warning(f"第 {attempt + 1} 次尝试：获取的跌停股池数据为空")

            except Exception as e:
                logging.warning(f"第 {attempt + 1} 次尝试获取跌停股池失败: {e}")
                if attempt < max_retries - 1:
                    error_delay()
                    logging.info(f"错误延时后进行第 {attempt + 2} 次重试...")
                else:
                    logging.error(f"跌停股池数据获取完全失败，已重试 {max_retries} 次")

        return pd.DataFrame()

    except Exception as e:
        logging.error(f"获取跌停股池数据发生未知错误: {e}")
        return pd.DataFrame()

def get_zb_pool_data(date=None):
    """获取炸板股池数据（强势股池）"""
    try:
        if date is None:
            date = datetime.now().strftime('%Y%m%d')

        logging.info(f"正在获取炸板股池数据（日期: {date}）...")

        # 添加初始随机延时
        random_delay()

        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                zb_pool_df = ak.stock_zt_pool_strong_em(date=date)

                if zb_pool_df is not None and not zb_pool_df.empty:
                    logging.info(f"✅ 成功获取炸板股池数据，共 {len(zb_pool_df)} 只股票")
                    return zb_pool_df
                else:
                    logging.warning(f"第 {attempt + 1} 次尝试：获取的炸板股池数据为空")

            except Exception as e:
                logging.warning(f"第 {attempt + 1} 次尝试获取炸板股池失败: {e}")
                if attempt < max_retries - 1:
                    error_delay()
                    logging.info(f"错误延时后进行第 {attempt + 2} 次重试...")
                else:
                    logging.error(f"炸板股池数据获取完全失败，已重试 {max_retries} 次")

        return pd.DataFrame()

    except Exception as e:
        logging.error(f"获取炸板股池数据发生未知错误: {e}")
        return pd.DataFrame()

def analyze_limit_up_industries(zt_pool_df):
    """分析涨停板数据，获取连板数最高的行业和涨停板最多的行业"""
    try:
        if zt_pool_df.empty:
            logging.warning("涨停股池数据为空，无法分析行业")
            return []

        # 检查必要的列是否存在
        required_columns = ['所属行业', '连板数']
        missing_columns = [col for col in required_columns if col not in zt_pool_df.columns]
        if missing_columns:
            logging.warning(f"涨停股池数据缺少必要列: {missing_columns}")
            return []

        logging.info("开始分析涨停板行业分布...")

        # 清理数据
        analysis_df = zt_pool_df.copy()
        analysis_df['所属行业'] = analysis_df['所属行业'].fillna('未知')
        analysis_df['连板数'] = pd.to_numeric(analysis_df['连板数'], errors='coerce').fillna(1)

        # 过滤掉无效行业
        analysis_df = analysis_df[analysis_df['所属行业'] != '未知']

        if analysis_df.empty:
            logging.warning("过滤后的涨停股池数据为空")
            return []

        target_industries = []

        # 1. 找出连板数最高的行业
        max_consecutive = analysis_df['连板数'].max()
        if max_consecutive > 1:
            max_consecutive_stocks = analysis_df[analysis_df['连板数'] == max_consecutive]
            consecutive_industry_count = max_consecutive_stocks['所属行业'].value_counts()

            if not consecutive_industry_count.empty:
                top_consecutive_industry = consecutive_industry_count.index[0]
                top_consecutive_count = consecutive_industry_count.iloc[0]
                target_industries.append(top_consecutive_industry)
                logging.info(f"连板数最高的行业: {top_consecutive_industry} ({top_consecutive_count}只股票, {max_consecutive}连板)")
                print(f"🔥 连板数最高的行业: {top_consecutive_industry} ({top_consecutive_count}只股票, {max_consecutive}连板)")

        # 2. 找出涨停板数量最多的行业（除了连板数最高的）
        industry_count = analysis_df['所属行业'].value_counts()

        # 获取前3个涨停板最多的行业，排除已经选择的连板数最高的行业
        top_industries = []
        for industry, count in industry_count.items():
            if industry not in target_industries:  # 排除已选择的行业
                top_industries.append((industry, count))
                if len(top_industries) >= 3:  # 最多取3个
                    break

        for industry, count in top_industries:
            target_industries.append(industry)
            logging.info(f"涨停板最多的行业: {industry} ({count}只股票)")
            print(f"📈 涨停板最多的行业: {industry} ({count}只股票)")

        # 去重并限制数量
        target_industries = list(dict.fromkeys(target_industries))  # 保持顺序的去重
        target_industries = target_industries[:4]  # 最多4个行业

        logging.info(f"分析完成，目标行业: {target_industries}")
        return target_industries

    except Exception as e:
        logging.error(f"分析涨停板行业失败: {e}")
        return []

def get_stock_individual_fund_flow_rank():
    """获取所有个股资金流排名数据"""
    try:
        logging.info("正在获取所有个股资金流排名数据...")

        # 添加初始随机延时
        random_delay()

        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 使用东方财富个股资金流排名接口
                stock_flow_df = ak.stock_individual_fund_flow_rank(indicator="今日")

                if stock_flow_df is not None and not stock_flow_df.empty:
                    logging.info(f"✅ 成功获取个股资金流排名数据，共 {len(stock_flow_df)} 只股票")
                    return stock_flow_df
                else:
                    logging.warning(f"第 {attempt + 1} 次尝试：获取的个股资金流排名数据为空")

            except Exception as e:
                logging.warning(f"第 {attempt + 1} 次尝试获取个股资金流排名失败: {e}")
                if attempt < max_retries - 1:
                    error_delay()
                    logging.info(f"错误延时后进行第 {attempt + 2} 次重试...")
                else:
                    logging.error(f"个股资金流排名数据获取完全失败，已重试 {max_retries} 次")

        return pd.DataFrame()

    except Exception as e:
        logging.error(f"获取个股资金流排名数据发生未知错误: {e}")
        return pd.DataFrame()

def get_sector_stock_fund_flow(sector_name, sector_type="行业", global_stock_flow_cache=None):
    """获取指定板块/概念的个股资金流数据

    Args:
        sector_name: 板块/概念名称
        sector_type: 类型，"行业" 或 "概念"
        global_stock_flow_cache: 预先获取的全市场个股资金流数据缓存，用于概念处理优化
    """
    try:
        logging.info(f"正在获取{sector_type} '{sector_name}' 的个股资金流数据...")

        # 添加随机延时，避免请求过于频繁
        random_delay()

        # 对于行业，直接使用 stock_sector_fund_flow_summary 接口
        if sector_type == "行业":
            # 获取行业板块名称缓存进行名称匹配
            names_df = get_industry_names_cache()
            exact_name = sector_name

            if not names_df.empty and '板块名称' in names_df.columns:
                # 首先尝试精确匹配
                exact_match = names_df[names_df['板块名称'] == sector_name]
                if not exact_match.empty:
                    exact_name = exact_match.iloc[0]['板块名称']
                    logging.info(f"找到精确匹配的行业名称: {exact_name}")
                else:
                    # 尝试模糊匹配
                    fuzzy_match = names_df[names_df['板块名称'].str.contains(sector_name, na=False)]
                    if not fuzzy_match.empty:
                        exact_name = fuzzy_match.iloc[0]['板块名称']
                        logging.info(f"找到模糊匹配的行业名称: {exact_name}")
                    else:
                        logging.warning(f"未找到匹配的行业名称: {sector_name}，使用原名称")

            # 使用 stock_sector_fund_flow_summary 接口获取行业个股资金流数据
            try:
                stock_df = ak.stock_sector_fund_flow_summary(symbol=exact_name, indicator="今日")
                if not stock_df.empty:
                    logging.info(f"✅ 使用 stock_sector_fund_flow_summary 成功获取行业 '{exact_name}' 的个股资金流数据，共 {len(stock_df)} 只股票")
                    return stock_df
                else:
                    logging.warning(f"行业 '{exact_name}' 的个股资金流数据为空")
            except Exception as e1:
                logging.warning(f"stock_sector_fund_flow_summary 接口失败: {e1}")

                # 如果使用精确名称失败，且精确名称与原名称不同，尝试使用原名称
                if exact_name != sector_name:
                    try:
                        logging.info(f"尝试使用原名称: {sector_name}")
                        # 添加错误延时后重试
                        error_delay()
                        stock_df = ak.stock_sector_fund_flow_summary(symbol=sector_name, indicator="今日")
                        if not stock_df.empty:
                            logging.info(f"✅ 使用原名称成功获取行业 '{sector_name}' 的个股资金流数据，共 {len(stock_df)} 只股票")
                            return stock_df
                        else:
                            logging.warning(f"行业 '{sector_name}' 的个股资金流数据为空")
                    except Exception as e2:
                        logging.error(f"使用原名称也失败: {e2}")

        # 对于概念，先尝试直接获取资金流，失败则获取成分股后再获取资金流
        elif sector_type == "概念":
            logging.info(f"概念板块 '{sector_name}' 尝试获取个股资金流...")

            # 获取概念板块名称缓存进行名称匹配
            names_df = get_concept_names_cache()
            exact_name = sector_name

            if not names_df.empty and '板块名称' in names_df.columns:
                # 首先尝试精确匹配
                exact_match = names_df[names_df['板块名称'] == sector_name]
                if not exact_match.empty:
                    exact_name = exact_match.iloc[0]['板块名称']
                    logging.info(f"找到精确匹配的概念名称: {exact_name}")
                else:
                    # 尝试模糊匹配
                    fuzzy_match = names_df[names_df['板块名称'].str.contains(sector_name.replace('概念', ''), na=False)]
                    if not fuzzy_match.empty:
                        exact_name = fuzzy_match.iloc[0]['板块名称']
                        logging.info(f"找到模糊匹配的概念名称: {exact_name}")
                    else:
                        logging.warning(f"未找到匹配的概念名称: {sector_name}，使用原名称")

            # 方法1：尝试使用 stock_sector_fund_flow_summary 接口获取概念个股资金流数据
            try:
                logging.info(f"方法1：尝试使用概念名称 '{exact_name}' 获取资金流...")
                stock_df = ak.stock_sector_fund_flow_summary(symbol=exact_name, indicator="今日")
                if not stock_df.empty:
                    logging.info(f"✅ 方法1成功：使用概念名称获取概念 '{exact_name}' 的个股资金流数据，共 {len(stock_df)} 只股票")
                    return stock_df
                else:
                    logging.warning(f"方法1失败：概念名称 '{exact_name}' 的个股资金流数据为空")
            except Exception as e1:
                logging.warning(f"方法1失败：使用概念名称 '{exact_name}' 获取资金流失败: {e1}")

                # 如果精确名称失败且与原名称不同，尝试原名称
                if exact_name != sector_name:
                    try:
                        logging.info(f"方法1b：尝试使用原始名称 '{sector_name}' 获取资金流...")
                        # 添加错误延时后重试
                        error_delay()
                        stock_df = ak.stock_sector_fund_flow_summary(symbol=sector_name, indicator="今日")
                        if not stock_df.empty:
                            logging.info(f"✅ 方法1b成功：使用原始名称获取概念 '{sector_name}' 的个股资金流数据，共 {len(stock_df)} 只股票")
                            return stock_df
                        else:
                            logging.warning(f"方法1b失败：原始名称 '{sector_name}' 的个股资金流数据为空")
                    except Exception as e2:
                        logging.warning(f"方法1b失败：使用原始名称 '{sector_name}' 获取资金流失败: {e2}")

            # 方法2：回退到获取概念成分股，然后获取个股资金流
            logging.info(f"方法2：回退到获取概念 '{exact_name}' 的成分股，然后获取个股资金流...")
            try:
                # 添加延时后获取成分股
                error_delay()
                concept_stocks = ak.stock_board_concept_cons_em(symbol=exact_name)
                if concept_stocks is not None and not concept_stocks.empty and '代码' in concept_stocks.columns:
                    logging.info(f"获取概念 '{exact_name}' 的成分股数据，共 {len(concept_stocks)} 只股票，正在获取个股资金流...")

                    # 方法2a：尝试使用通用个股资金流接口
                    try:
                        # 优先使用缓存的全市场数据
                        if global_stock_flow_cache is not None and not global_stock_flow_cache.empty:
                            logging.info(f"使用缓存的全市场个股资金流数据，共 {len(global_stock_flow_cache)} 只股票")
                            stock_flow_df = global_stock_flow_cache.copy()
                        else:
                            logging.info(f"正在获取全市场个股资金流数据...")
                            # 添加错误延时后获取个股资金流
                            error_delay()
                            stock_flow_df = ak.stock_individual_fund_flow_rank(indicator="今日")
                            if not stock_flow_df.empty and '代码' in stock_flow_df.columns:
                                logging.info(f"成功获取全市场个股资金流数据，共 {len(stock_flow_df)} 只股票")
                                stock_flow_df['代码'] = stock_flow_df['代码'].astype(str).str.zfill(6)
                            else:
                                logging.warning(f"方法2a失败：无法获取个股资金流数据或数据为空")
                                raise Exception("无法获取个股资金流数据")

                        # 筛选出概念成分股的资金流数据
                        concept_codes = set(concept_stocks['代码'].astype(str).str.zfill(6))
                        filtered_flow_df = stock_flow_df[stock_flow_df['代码'].isin(concept_codes)]

                        if not filtered_flow_df.empty:
                            # 验证是否为真正的资金流数据
                            fund_flow_columns = [col for col in filtered_flow_df.columns if '净流入' in col or '净额' in col]
                            if fund_flow_columns:
                                logging.info(f"✅ 方法2a成功：获取概念 '{exact_name}' 的个股资金流数据，共 {len(filtered_flow_df)} 只股票")
                                logging.info(f"资金流数据列: {fund_flow_columns[:3]}...")
                                return filtered_flow_df
                            else:
                                logging.warning(f"方法2a失败：筛选结果不包含资金流数据列")
                        else:
                            logging.warning(f"方法2a失败：未找到概念成分股的资金流数据，可能是代码匹配问题")
                    except Exception as e3:
                        logging.warning(f"方法2a失败：获取个股资金流数据异常: {e3}")

                    # 方法2b：尝试使用东方财富个股资金流接口
                    try:
                        logging.info(f"方法2b：尝试使用东方财富个股资金流接口...")
                        # 添加错误延时后获取东方财富个股资金流
                        error_delay()
                        stock_flow_df = ak.stock_fund_flow_individual(symbol="全部")
                        if not stock_flow_df.empty and '代码' in stock_flow_df.columns:
                            logging.info(f"东方财富接口成功获取个股资金流数据，共 {len(stock_flow_df)} 只股票")

                            # 筛选出概念成分股的资金流数据
                            concept_codes = set(concept_stocks['代码'].astype(str).str.zfill(6))
                            stock_flow_df['代码'] = stock_flow_df['代码'].astype(str).str.zfill(6)
                            filtered_flow_df = stock_flow_df[stock_flow_df['代码'].isin(concept_codes)]

                            if not filtered_flow_df.empty:
                                # 验证是否为真正的资金流数据
                                fund_flow_columns = [col for col in filtered_flow_df.columns if '净流入' in col or '净额' in col or '资金' in col]
                                if fund_flow_columns:
                                    logging.info(f"✅ 方法2b成功：获取概念 '{exact_name}' 的个股资金流数据，共 {len(filtered_flow_df)} 只股票")
                                    logging.info(f"资金流数据列: {fund_flow_columns[:3]}...")
                                    return filtered_flow_df
                                else:
                                    logging.warning(f"方法2b失败：筛选结果不包含资金流数据列")
                            else:
                                logging.warning(f"方法2b失败：未找到概念成分股的资金流数据")
                        else:
                            logging.warning(f"方法2b失败：东方财富接口无法获取数据")
                    except Exception as e4:
                        logging.warning(f"方法2b失败：东方财富接口异常: {e4}")

                    # 如果所有方法都失败，返回空DataFrame，不返回成分股数据
                    logging.error(f"所有方法都失败：无法获取概念 '{exact_name}' 的个股资金流数据")
                    return pd.DataFrame()
                else:
                    logging.warning(f"概念 '{exact_name}' 的成分股数据为空")
            except Exception as e3:
                logging.error(f"获取概念成分股失败: {e3}")

                # 如果使用精确名称失败，且精确名称与原名称不同，尝试使用原名称
                if exact_name != sector_name:
                    try:
                        logging.info(f"方法2c：尝试使用原始名称 '{sector_name}' 获取成分股...")
                        # 添加错误延时后重试获取成分股
                        error_delay()
                        concept_stocks = ak.stock_board_concept_cons_em(symbol=sector_name)
                        if concept_stocks is not None and not concept_stocks.empty and '代码' in concept_stocks.columns:
                            logging.info(f"获取概念 '{sector_name}' 的成分股数据，共 {len(concept_stocks)} 只股票，正在获取个股资金流...")

                            # 同样尝试获取个股资金流
                            try:
                                # 优先使用缓存的全市场数据
                                if global_stock_flow_cache is not None and not global_stock_flow_cache.empty:
                                    logging.info(f"使用缓存的全市场个股资金流数据，共 {len(global_stock_flow_cache)} 只股票")
                                    stock_flow_df = global_stock_flow_cache.copy()
                                else:
                                    logging.info(f"正在获取全市场个股资金流数据...")
                                    # 添加错误延时后获取个股资金流
                                    error_delay()
                                    stock_flow_df = ak.stock_individual_fund_flow_rank(indicator="今日")
                                    if not stock_flow_df.empty and '代码' in stock_flow_df.columns:
                                        stock_flow_df['代码'] = stock_flow_df['代码'].astype(str).str.zfill(6)
                                    else:
                                        raise Exception("无法获取个股资金流数据")

                                concept_codes = set(concept_stocks['代码'].astype(str).str.zfill(6))
                                filtered_flow_df = stock_flow_df[stock_flow_df['代码'].isin(concept_codes)]

                                if not filtered_flow_df.empty:
                                    # 验证是否为真正的资金流数据
                                    fund_flow_columns = [col for col in filtered_flow_df.columns if '净流入' in col or '净额' in col]
                                    if fund_flow_columns:
                                        logging.info(f"✅ 方法2c成功：获取概念 '{sector_name}' 的个股资金流数据，共 {len(filtered_flow_df)} 只股票")
                                        return filtered_flow_df
                                    else:
                                        logging.warning(f"方法2c失败：筛选结果不包含资金流数据列")
                                else:
                                    logging.warning(f"方法2c失败：未找到概念成分股的资金流数据")
                            except Exception as e5:
                                logging.warning(f"方法2c获取个股资金流失败: {e5}")

                            # 如果无法获取资金流数据，返回空DataFrame，不返回成分股数据
                            logging.error(f"方法2c失败：无法获取概念 '{sector_name}' 的个股资金流数据")
                        else:
                            logging.warning(f"概念 '{sector_name}' 的成分股数据为空")
                    except Exception as e4:
                        logging.error(f"使用原名称获取概念成分股也失败: {e4}")

        return pd.DataFrame()

    except Exception as e:
        logging.error(f"获取{sector_type} '{sector_name}' 的个股数据完全失败: {e}")
        return pd.DataFrame()

def print_top_sectors(df, sector_type, top_n=None):
    """打印并返回前N名的板块/概念"""
    if top_n is None:
        top_n = TOP_N
        
    if df.empty:
        logging.warning(f"无{sector_type}数据可显示")
        return []
    
    # 按资金流入排序并取前N名
    top_df = df.sort_values('今日主力净流入-净额', ascending=False).head(top_n)
    
    print(f"\n{'='*20} {sector_type}资金流入前{top_n}名 {'='*20}")
    
    # 格式化显示数据
    display_df = top_df.copy()
    if '今日主力净流入-净额' in display_df.columns:
        display_df['今日主力净流入-净额'] = display_df['今日主力净流入-净额'].apply(format_amount)
    if '今日涨跌幅' in display_df.columns:
        display_df['今日涨跌幅'] = display_df['今日涨跌幅'].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else "0.00%")
    
    # 显示表格
    print(tabulate(display_df[['名称', '今日涨跌幅', '今日主力净流入-净额']], 
                   headers=['名称', '涨跌幅', '主力净流入'], 
                   tablefmt='psql', 
                   showindex=False))
    
    # 返回前N名的名称列表
    top_names = top_df['名称'].tolist()
    logging.info(f"{sector_type}资金流入前{top_n}名: {', '.join(top_names)}")
    
    return top_names

def save_data(df, filename):
    """保存数据到CSV文件"""
    if df.empty:
        logging.warning(f"数据为空，跳过保存文件: {filename}")
        return

    try:
        date_folder = get_date_folder()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_path = os.path.join(date_folder, f'{filename}_{timestamp}.csv')

        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        logging.info(f"成功保存数据到: {file_path}")
        print(f"✅ 保存: {file_path}")

    except Exception as e:
        logging.error(f"保存文件失败: {filename}, 错误: {e}")

def process_sector_stocks(sector_names, sector_type="行业"):
    """处理板块列表，获取每个板块的个股数据"""
    # 对于概念类型，预先获取全市场个股资金流数据以供重复使用
    global_stock_flow_cache = None
    if sector_type == "概念":
        try:
            logging.info("预先获取全市场个股资金流数据以供概念处理重复使用...")
            error_delay()
            global_stock_flow_cache = ak.stock_individual_fund_flow_rank(indicator="今日")
            if not global_stock_flow_cache.empty and '代码' in global_stock_flow_cache.columns:
                logging.info(f"成功获取全市场个股资金流数据，共 {len(global_stock_flow_cache)} 只股票，将用于所有概念处理")
                global_stock_flow_cache['代码'] = global_stock_flow_cache['代码'].astype(str).str.zfill(6)
            else:
                logging.warning("预先获取全市场个股资金流数据失败，将回退到逐个获取模式")
                global_stock_flow_cache = None
        except Exception as e:
            logging.warning(f"预先获取全市场个股资金流数据异常: {e}，将回退到逐个获取模式")
            global_stock_flow_cache = None

    for i, sector_name in enumerate(sector_names, 1):
        try:
            logging.info(f"处理第 {i}/{len(sector_names)} 个{sector_type}: {sector_name}")

            # 获取个股数据，传入缓存的全市场数据
            stock_df = get_sector_stock_fund_flow(sector_name, sector_type, global_stock_flow_cache)

            if not stock_df.empty:
                # 清理文件名并保存
                safe_name = safe_clean_filename(sector_name)
                filename = f"{sector_type}_stocks_{safe_name}"
                save_data(stock_df, filename)

                # 显示该板块的前5只个股
                print(f"\n--- {sector_type}: {sector_name} (前5只个股) ---")
                top_5_stocks = stock_df.head(5)
                display_stocks = top_5_stocks.copy()

                # 根据数据格式灵活处理显示列
                cols_to_show = []
                if '代码' in display_stocks.columns:
                    cols_to_show.append('代码')
                if '名称' in display_stocks.columns:
                    cols_to_show.append('名称')
                elif '股票简称' in display_stocks.columns:
                    cols_to_show.append('股票简称')
                    display_stocks['名称'] = display_stocks['股票简称']
                    cols_to_show.append('名称')

                # 如果有资金流数据，则格式化显示
                if '今日涨跌幅' in display_stocks.columns:
                    display_stocks['今日涨跌幅'] = display_stocks['今日涨跌幅'].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else "0.00%")
                    cols_to_show.append('今日涨跌幅')

                if '今日主力净流入-净额' in display_stocks.columns:
                    display_stocks['今日主力净流入-净额'] = display_stocks['今日主力净流入-净额'].apply(format_amount)
                    cols_to_show.append('今日主力净流入-净额')

                # 如果没有资金流数据，至少显示基本信息
                if len(cols_to_show) < 3:
                    for col in ['最新价', '涨跌幅', '成交量', '流通市值']:
                        if col in display_stocks.columns:
                            cols_to_show.append(col)
                            if len(cols_to_show) >= 4:  # 最多显示4列
                                break

                if cols_to_show:
                    print(tabulate(display_stocks[cols_to_show],
                                 headers=cols_to_show,
                                 tablefmt='psql',
                                 showindex=False))
                else:
                    print(f"✅ 获取到 {len(stock_df)} 只成分股，但无详细数据可显示")

            # 随机延时，避免请求过于频繁
            if i < len(sector_names):  # 最后一个不需要延时
                random_delay()

        except Exception as e:
            logging.error(f"处理{sector_type} '{sector_name}' 时发生错误: {e}")
            continue

def main_task():
    """主要任务：获取概念、行业资金流数据"""
    try:
        # 检查是否在交易时间内
        if not is_trading_time():
            current_time = datetime.now().strftime('%H:%M:%S')
            next_time = get_next_trading_time()
            msg = f"⏰ 非交易时间（当前: {current_time}），跳过本次获取。下次交易时间: {next_time}"
            logging.info(msg)
            print(msg)
            return

        start_time = datetime.now()
        logging.info("="*50)
        logging.info("开始执行概念、行业资金流获取任务")
        logging.info("="*50)

        success_count = 0  # 记录成功获取的数据类型数量

        # 1. 【修改】首先获取涨停股池数据
        logging.info("--- 开始获取涨停股池数据 ---")
        zt_pool_df = get_zt_pool_data()
        limit_up_target_industries = []  # 存储涨停板分析得出的目标行业

        if not zt_pool_df.empty:
            save_data(zt_pool_df, "zt_pool")
            print(f"\n✅ 涨停股池数据获取成功，共 {len(zt_pool_df)} 只股票")

            # 分析涨停板数据，获取目标行业
            limit_up_target_industries = analyze_limit_up_industries(zt_pool_df)
            success_count += 1
        else:
            logging.warning("涨停股池数据获取失败")
            print("⚠️ 涨停股池数据获取失败")

        random_delay()

        # 2. 获取行业资金流数据
        logging.info("--- 开始获取行业资金流数据 ---")
        industry_df = get_industry_fund_flow()
        if not industry_df.empty:
            # 添加类型标识
            industry_df['type'] = '行业'
            # 保存完整的行业数据
            save_data(industry_df, "industry_fund_flow_rank")

            # 【修改】获取并打印前5名行业（原来是10名）
            top_industries = print_top_sectors(industry_df, "行业", 5)

            # 获取前5名行业的个股数据
            if top_industries:
                logging.info(f"开始获取前{len(top_industries)}名行业的个股数据...")
                process_sector_stocks(top_industries, "行业")

            success_count += 1
        else:
            logging.warning("行业资金流数据获取失败，跳过行业数据处理")
            print("⚠️ 行业资金流数据获取失败")

        random_delay()

        # 3. 获取概念资金流数据
        logging.info("--- 开始获取概念资金流数据 ---")
        concept_df = get_concept_fund_flow()
        if not concept_df.empty:
            # 保存完整的概念数据
            save_data(concept_df, "concept_fund_flow_rank")

            # 【修改】获取并打印前5名概念（原来是10名）
            top_concepts = print_top_sectors(concept_df, "概念", 5)

            # 获取前5名概念的个股数据
            if top_concepts:
                logging.info(f"开始获取前{len(top_concepts)}名概念的个股数据...")
                process_sector_stocks(top_concepts, "概念")

            success_count += 1
        else:
            logging.warning("概念资金流数据获取失败，跳过概念数据处理")
            print("⚠️ 概念资金流数据获取失败")

        random_delay()

        # 4. 【新增】获取涨停板分析得出的目标行业资金流数据
        if limit_up_target_industries:
            logging.info("--- 开始获取涨停板目标行业资金流数据 ---")
            print(f"\n🎯 开始获取涨停板分析目标行业资金流数据...")

            # 获取这些行业的资金流数据
            process_sector_stocks(limit_up_target_industries, "行业")

            logging.info(f"涨停板目标行业资金流数据获取完成，共处理 {len(limit_up_target_industries)} 个行业")
            print(f"✅ 涨停板目标行业资金流数据获取完成")
        else:
            logging.info("没有涨停板目标行业需要处理")

        random_delay()

        random_delay()

        # 5. 获取所有个股资金流排名数据
        logging.info("--- 开始获取所有个股资金流排名数据 ---")
        stock_flow_rank_df = get_stock_individual_fund_flow_rank()
        if not stock_flow_rank_df.empty:
            save_data(stock_flow_rank_df, "fund_flow_rank")
            print(f"\n✅ 个股资金流排名数据获取成功，共 {len(stock_flow_rank_df)} 只股票")
            success_count += 1
        else:
            logging.warning("个股资金流排名数据获取失败")
            print("⚠️ 个股资金流排名数据获取失败")

        random_delay()

        # 6. 获取跌停股池数据
        logging.info("--- 开始获取跌停股池数据 ---")
        dt_pool_df = get_dt_pool_data()
        if not dt_pool_df.empty:
            save_data(dt_pool_df, "dt_pool")
            print(f"\n✅ 跌停股池数据获取成功，共 {len(dt_pool_df)} 只股票")
            success_count += 1
        else:
            logging.warning("跌停股池数据获取失败")
            print("⚠️ 跌停股池数据获取失败")

        random_delay()

        # 7. 获取炸板股池数据
        logging.info("--- 开始获取炸板股池数据 ---")
        zb_pool_df = get_zb_pool_data()
        if not zb_pool_df.empty:
            save_data(zb_pool_df, "zb_pool")
            print(f"\n✅ 炸板股池数据获取成功，共 {len(zb_pool_df)} 只股票")
            success_count += 1
        else:
            logging.warning("炸板股池数据获取失败")
            print("⚠️ 炸板股池数据获取失败")

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 任务完成总结
        print(f"\n{'='*50}")
        if success_count > 0:
            print(f"✅ 任务部分完成！成功获取 {success_count}/7 类数据，总耗时: {duration:.2f} 秒")
            if limit_up_target_industries:
                print(f"🎯 涨停板目标行业: {', '.join(limit_up_target_industries)}")
        else:
            print(f"❌ 任务失败！所有数据获取均失败，总耗时: {duration:.2f} 秒")
        print(f"数据保存在: {get_date_folder()}")
        print(f"{'='*50}")

        logging.info(f"任务完成，成功获取 {success_count}/7 类数据，总耗时: {duration:.2f} 秒")
        
    except Exception as e:
        logging.error(f"主任务执行失败: {e}")
        print(f"❌ 任务执行失败: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def schedule_task():
    """定时任务：每3分钟执行一次"""
    schedule.every(SCHEDULE_MINUTES).minutes.do(main_task)
    logging.info(f"已设置定时任务：每{SCHEDULE_MINUTES}分钟获取一次概念、行业资金流数据")
    print(f"⏰ 定时任务已启动：每{SCHEDULE_MINUTES}分钟获取一次数据")
    print("按 Ctrl+C 停止程序")
    
    # 立即执行一次
    main_task()
    
    # 进入定时循环
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except KeyboardInterrupt:
            print("\n程序已停止")
            logging.info("程序被用户中断")
            break
        except Exception as e:
            logging.error(f"定时任务执行错误: {e}")

if __name__ == "__main__":
    # 设置日志
    setup_logging()
    
    # 显示当前交易时间状态
    current_time = datetime.now().strftime('%H:%M:%S')
    is_trading = is_trading_time()
    next_time = get_next_trading_time() if not is_trading else "正在交易中"
    
    print("概念、行业资金流及股池获取工具 v2.4")
    print("="*50)
    print("功能：")
    print("1. 【优先】获取涨停股池数据并分析目标行业（带重试机制）")
    print("2. 获取行业资金流排名（带重试机制）")
    print("3. 获取概念资金流排名（带重试机制）")
    print(f"4. 自动获取前{TOP_N}名概念和行业的个股资金流")
    print("5. 【新增】获取涨停板连板数最高行业的资金流数据")
    print("6. 【新增】获取涨停板数量最多行业的资金流数据")
    print("7. 获取所有个股资金流排名数据（带重试机制）")
    print("8. 获取跌停股池数据（带重试机制）")
    print("9. 获取炸板股池数据（带重试机制）")
    print(f"10. 定时获取（每{SCHEDULE_MINUTES}分钟一次）")
    print(f"11. 智能交易时间判断（09:15-11:30/13:00-15:00）")
    print(f"12. 数据自动保存到 {BASE_DATA_PATH}/日期/ 文件夹")
    print("13. 多重备用接口，提高成功率")
    print("14. 详细的错误处理和日志记录")
    print("15. 随机延时避免请求过于频繁")
    print("="*50)
    print(f"📁 数据将保存到: {get_date_folder()}")
    print(f"⏰ 当前时间: {current_time} | 交易状态: {'🟢 交易中' if is_trading else '🔴 休市'}")
    if not is_trading:
        print(f"⏰ 下次交易时间: {next_time}")
    print("="*50)
    
    try:
        # 启动定时任务
        schedule_task()
        
    except Exception as e:
        logging.error(f"程序启动失败: {e}")
        print(f"❌ 程序启动失败: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")