任务是根据我提供的优化方案，对我现有的Python脚本 dynamic_gap_detector.py 进行最小化且精确的修改，以实现一个新的“主线强度评分”功能。

特别注意，你先要检查我的代码是否有板块匹配的功能，即是识别个股概念行业的功能，如果没有，你需要使用参考akshare_data_fetcher.py来实现get_stock_sectors 获取概念、行业等映射关系的缓存，在文件头定义一个更新缓存概念行业的功能，默认为关，如果打开后启动程序先去更新缓存，我的akshare_data_fetcher.py已经实现了概念缓存的获取和更新功能，你根据我的需求，看是否需要修改

核心任务:
在现有的 HistoricalBreakthroughDetector 信号检测逻辑基础上，集成一个全新的、量化的“主线强度评分”体系。你需要新增一个评分类，并修改现有代码，使得每个生成的历史突破信号都附带一个“主线强度分”、“评级”和“评分理由”。
上下文:
我提供完整的 dynamic_gap_detector.py 脚本代码。该脚本会按时间顺序处理盘中数据文件，分析资金流向，并检测多种市场信号。其中，HistoricalBreakthroughDetector 类负责检测“二次点火”、“横空出世”等买入信号。当前的挑战是，这些信号没有考虑当时的市场主线热点，导致信号质量参差不齐。
优化方案详解:
你必须严格遵循以下优化方案来实现代码修改。
1. 引入“主线强度分 (Mainline Strength Score)”体系:
你需要创建一个评分体系，当 HistoricalBreakthroughDetector 检测到一个信号时，立即根据当时的盘面数据为其计算一个附加的“主线强度分”。
2. 评分标准 (Score Criteria):
评分逻辑必须严格按照下表实现。总分是各项分值的累加。
评分项	触发条件 (基于盘中日志)	分值
资金驱动维度		
个股龙头地位	个股本身是“个股资金流发现资金断层”的断层龙头	+10
板块龙头地位	股票所属的行业/概念板块是“资金断层”的断层龙头	+8
板块资金排名 (取最高分)	股票所属的行业/概念板块，主力净流入排名第1	+5
股票所属的行业/概念板块，主力净流入排名第2	+4
股票所属的行业/概念板块，主力净流入排名第3	+3
情绪/赚钱效应维度		
板块梯队强度 (连板)	股票所属行业是“连板数最多行业”	+5
股票所属行业是“N板连板数最多行业” (N>=3)	+4
股票所属行业是“2板连板数最多行业”	+4
板块梯队强度 (涨停)	股票所属行业是“涨停行业最多的”	+4
股票所属行业是“首扳最多行业”	+3
动态变化维度		
板块资金加速	股票所属的行业/概念板块触发了【资金加速度警报!】	+8
3. 信号评级标准:
根据计算出的“主线强度分”，为每个信号生成一个评级字符串：
★★★ [优先]: 主线强度分 >= 10分
★★☆ [关注]: 5分 <= 主线强度分 < 10分
★☆☆ [观察]: 主线强度分 < 5分
实现步骤与代码修改要求:


新增 MainlineStrengthScorer 类:
创建一个名为 MainlineStrengthScorer 的新类。
该类应包含一个 score_signal(self, signal, market_snapshot) 方法。此方法接收一个信号字典和包含当前所有盘面核心信息的 market_snapshot 字典。
score_signal 方法需实现上述完整的评分逻辑，并返回三个值：主线强度分 (int), 评级 (str), 评分理由 (list of strings)。

为了实现板块匹配，你需要实现一个 get_stock_sectors(self, stock_name, stock_code) 方法。此方法需要依赖一个外部的股票-板块映射关系。如果我现在的代码没有，你需要按照要求去获取数据实现


修改 HistoricalBreakthroughDetector 类:
在 __init__ 方法中，实例化你创建的 MainlineStrengthScorer 类。
修改 detect_signals 方法的函数签名，使其能够接收一个 market_snapshot 字典作为新参数。
在 detect_signals 方法内部，当一个原始信号被检测到后，必须调用 MainlineStrengthScorer 的实例来计算主线分和评级。
将计算出的 主线强度分、评级 和 评分理由 添加到最终的信号字典中。
确保股票代码 stock_code 在数据处理和信号生成过程中被正确传递，以便 MainlineStrengthScorer 可以进行更精确的板块匹配。
修改 _run_analysis_core 函数:
这是核心的调度函数。你需要在每个时间点的循环开始时，初始化一个名为 market_snapshot 的空字典。
在循环体内，当代码解析完个股资金流断层、板块资金流断层、资金加速度警报和涨停股池统计等信息后，你必须将这些关键的、结构化的信息填充到 market_snapshot 字典中。例如：market_snapshot['stock_gap_leader'] = '上海电气'，market_snapshot['sector_top_1'] = ['通信设备'] 等。
在调用 historical_breakthrough_detector.detect_signals 时，将这个填充完毕的 market_snapshot 字典作为参数传递进去。
修改最终打印信号表格和写入信号日志的代码，增加“评级”、“主线分”、“评分理由”这几列，并确保信号列表首先根据“主线强度分”进行降序排序，其次根据“综合评分”进行降序排序