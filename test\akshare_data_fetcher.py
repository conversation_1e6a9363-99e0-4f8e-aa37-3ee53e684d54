#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
akshare数据获取模块
专门负责从akshare获取概念和行业数据，并储备为本地"粮草"
心法：兵马未动，粮草先行
"""

import akshare as ak
import json
import time
import random
import os
from tqdm import tqdm
import datetime

# 数据保存目录
DATA_SAVE_DIR = "akshare_data"

def ensure_ak_data_dir():
    """确保akshare数据目录存在"""
    if not os.path.exists(DATA_SAVE_DIR):
        os.makedirs(DATA_SAVE_DIR)
        print(f">>> 创建akshare数据目录: {DATA_SAVE_DIR}")


def save_progress(progress_file, data_map, processed_items):
    """保存进度到文件"""
    try:
        progress_data = {
            'data': data_map,
            'processed': list(processed_items),
            'timestamp': datetime.datetime.now().isoformat()
        }
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f">>> [警告] 保存进度失败: {e}")

def fetch_ak_concepts_and_save():
    """
    从akshare获取所有概念板块及其成分股，并保存为'股票->概念'的映射
    心法：聚沙成塔，积少成多
    """
    print(">>> [akshare] 开始获取概念板块数据...")

    try:
        # 1. 获取所有概念板块名称
        print(">>> 正在获取概念板块名称列表...")
        concept_names_df = ak.stock_board_concept_name_em()
        print(f">>> 发现 {len(concept_names_df)} 个概念板块")

        # 检查是否有进度文件（用于断点续传）
        progress_file = os.path.join(DATA_SAVE_DIR, "concept_progress.json")
        stock_concept_map = {}
        processed_concepts = set()

        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    stock_concept_map = progress_data.get('data', {})
                    processed_concepts = set(progress_data.get('processed', []))
                print(f">>> 发现进度文件，已处理 {len(processed_concepts)} 个概念板块")
            except:
                print(">>> 进度文件损坏，重新开始")

        success_count = len(processed_concepts)
        error_count = 0
        
        # 2. 遍历每个概念，获取成分股
        for idx, row in tqdm(concept_names_df.iterrows(),
                           total=len(concept_names_df),
                           desc="获取概念成分股"):
            concept_name = row['板块名称']

            # 跳过已处理的概念
            if concept_name in processed_concepts:
                continue
            
            # 增强的重试机制
            max_retries = 3
            retry_count = 0
            success = False

            while retry_count < max_retries and not success:
                try:
                    # 获取该概念的成分股
                    cons_df = ak.stock_board_concept_cons_em(symbol=concept_name)

                    for _, stock_row in cons_df.iterrows():
                        code = stock_row['代码']

                        # 格式化代码，与xtquant保持一致
                        if code.startswith('6'):
                            stock_code = f"{code}.SH"
                        elif code.startswith('0') or code.startswith('3'):
                            stock_code = f"{code}.SZ"
                        elif code.startswith('8'):
                            stock_code = f"{code}.BJ"  # 北交所
                        else:
                            stock_code = f"{code}.SZ"  # 默认深交所

                        # 建立股票->概念的映射
                        if stock_code not in stock_concept_map:
                            stock_concept_map[stock_code] = []
                        stock_concept_map[stock_code].append(concept_name)

                    success_count += 1
                    success = True
                    processed_concepts.add(concept_name)

                    # 每处理10个概念保存一次进度
                    if len(processed_concepts) % 10 == 0:
                        save_progress(progress_file, stock_concept_map, processed_concepts)

                    # 核心：随机延时，避免被封禁
                    time.sleep(random.uniform(0.8, 2.0))

                except Exception as e:
                    retry_count += 1
                    error_msg = str(e)

                    # 判断错误类型，决定重试策略
                    if any(keyword in error_msg.lower() for keyword in ['connection', 'proxy', 'timeout', 'max retries']):
                        wait_time = min(5 + retry_count * 2, 15)  # 递增等待时间，最多15秒
                        print(f"\n>>> [重试 {retry_count}/{max_retries}] 概念板块 '{concept_name}' 网络错误，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        # 其他类型错误，直接跳过
                        print(f"\n>>> [跳过] 概念板块 '{concept_name}' 数据错误: {error_msg[:100]}...")
                        break

            if not success:
                error_count += 1
                print(f"\n>>> [失败] 概念板块 '{concept_name}' 重试 {max_retries} 次后仍然失败")
                # 失败后额外休息
                time.sleep(random.uniform(2.0, 4.0))
        
        # 3. 保存到文件
        ensure_ak_data_dir()
        filepath = os.path.join(DATA_SAVE_DIR, "ak_stock_concept_map.json")
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(stock_concept_map, f, ensure_ascii=False, indent=2)

        # 清理进度文件
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(">>> 清理进度文件")

        print(f"\n>>> [akshare] 概念板块数据获取完成")
        print(f">>> 成功: {success_count}, 失败: {error_count}")
        print(f">>> 覆盖股票: {len(stock_concept_map)} 只")
        print(f">>> 已保存至: {filepath}")

        return True
        
    except Exception as e:
        print(f">>> [错误] 获取akshare概念数据时发生严重错误: {e}")
        return False

def fetch_ak_industries_and_save():
    """
    从akshare获取所有行业板块及其成分股，并保存为'股票->行业'的映射
    心法：分门别类，井然有序
    """
    print(">>> [akshare] 开始获取行业板块数据...")
    
    try:
        # 1. 获取所有行业板块名称
        print(">>> 正在获取行业板块名称列表...")
        industry_names_df = ak.stock_board_industry_name_em()
        print(f">>> 发现 {len(industry_names_df)} 个行业板块")

        # 检查是否有进度文件（用于断点续传）
        progress_file = os.path.join(DATA_SAVE_DIR, "industry_progress.json")
        stock_industry_map = {}
        processed_industries = set()

        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    stock_industry_map = progress_data.get('data', {})
                    processed_industries = set(progress_data.get('processed', []))
                print(f">>> 发现进度文件，已处理 {len(processed_industries)} 个行业板块")
            except:
                print(">>> 进度文件损坏，重新开始")

        success_count = len(processed_industries)
        error_count = 0
        
        # 2. 遍历每个行业，获取成分股
        for idx, row in tqdm(industry_names_df.iterrows(),
                           total=len(industry_names_df),
                           desc="获取行业成分股"):
            industry_name = row['板块名称']

            # 跳过已处理的行业
            if industry_name in processed_industries:
                continue
            
            # 增强的重试机制
            max_retries = 3
            retry_count = 0
            success = False

            while retry_count < max_retries and not success:
                try:
                    # 获取该行业的成分股
                    cons_df = ak.stock_board_industry_cons_em(symbol=industry_name)

                    for _, stock_row in cons_df.iterrows():
                        code = stock_row['代码']

                        # 格式化代码，与xtquant保持一致
                        if code.startswith('6'):
                            stock_code = f"{code}.SH"
                        elif code.startswith('0') or code.startswith('3'):
                            stock_code = f"{code}.SZ"
                        elif code.startswith('8'):
                            stock_code = f"{code}.BJ"  # 北交所
                        else:
                            stock_code = f"{code}.SZ"  # 默认深交所

                        # 建立股票->行业的映射
                        if stock_code not in stock_industry_map:
                            stock_industry_map[stock_code] = []
                        stock_industry_map[stock_code].append(industry_name)

                    success_count += 1
                    success = True
                    processed_industries.add(industry_name)

                    # 每处理10个行业保存一次进度
                    if len(processed_industries) % 10 == 0:
                        save_progress(progress_file, stock_industry_map, processed_industries)

                    # 核心：随机延时，避免被封禁
                    time.sleep(random.uniform(0.8, 2.0))

                except Exception as e:
                    retry_count += 1
                    error_msg = str(e)

                    # 判断错误类型，决定重试策略
                    if any(keyword in error_msg.lower() for keyword in ['connection', 'proxy', 'timeout', 'max retries']):
                        wait_time = min(5 + retry_count * 2, 15)  # 递增等待时间，最多15秒
                        print(f"\n>>> [重试 {retry_count}/{max_retries}] 行业板块 '{industry_name}' 网络错误，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        # 其他类型错误，直接跳过
                        print(f"\n>>> [跳过] 行业板块 '{industry_name}' 数据错误: {error_msg[:100]}...")
                        break

            if not success:
                error_count += 1
                print(f"\n>>> [失败] 行业板块 '{industry_name}' 重试 {max_retries} 次后仍然失败")
                # 失败后额外休息
                time.sleep(random.uniform(2.0, 4.0))
        
        # 3. 保存到文件
        ensure_ak_data_dir()
        filepath = os.path.join(DATA_SAVE_DIR, "ak_stock_industry_map.json")
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(stock_industry_map, f, ensure_ascii=False, indent=2)

        # 清理进度文件
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(">>> 清理进度文件")

        print(f"\n>>> [akshare] 行业板块数据获取完成")
        print(f">>> 成功: {success_count}, 失败: {error_count}")
        print(f">>> 覆盖股票: {len(stock_industry_map)} 只")
        print(f">>> 已保存至: {filepath}")

        return True
        
    except Exception as e:
        print(f">>> [错误] 获取akshare行业数据时发生严重错误: {e}")
        return False

def check_data_freshness():
    """
    检查数据新鲜度，判断是否需要更新
    心法：温故知新，适时更新
    """
    concept_file = os.path.join(DATA_SAVE_DIR, "ak_stock_concept_map.json")
    industry_file = os.path.join(DATA_SAVE_DIR, "ak_stock_industry_map.json")
    
    files_info = []
    
    for filepath, name in [(concept_file, "概念数据"), (industry_file, "行业数据")]:
        if os.path.exists(filepath):
            # 获取文件修改时间
            mtime = os.path.getmtime(filepath)
            mtime_str = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            
            # 计算距离现在的天数
            days_old = (datetime.datetime.now().timestamp() - mtime) / (24 * 3600)
            
            files_info.append({
                'name': name,
                'path': filepath,
                'mtime': mtime_str,
                'days_old': days_old,
                'exists': True
            })
        else:
            files_info.append({
                'name': name,
                'path': filepath,
                'exists': False
            })
    
    return files_info

def print_data_status():
    """打印数据状态信息"""
    print("=" * 60)
    print("📊 akshare数据状态检查")
    print("=" * 60)
    
    files_info = check_data_freshness()
    
    for info in files_info:
        if info['exists']:
            status = "🟢 存在" if info['days_old'] < 7 else "🟡 较旧" if info['days_old'] < 30 else "🔴 过期"
            print(f"{info['name']}: {status}")
            print(f"   文件: {info['path']}")
            print(f"   更新时间: {info['mtime']}")
            print(f"   距今: {info['days_old']:.1f} 天")
        else:
            print(f"{info['name']}: ❌ 不存在")
            print(f"   文件: {info['path']}")
        print()
    
    # 给出建议
    need_update = any(not info['exists'] or info['days_old'] > 7 for info in files_info)
    if need_update:
        print("💡 建议: 数据需要更新，请运行数据获取功能")
    else:
        print("✅ 数据状态良好，无需更新")

def main():
    """主函数：执行完整的数据获取流程"""
    print("🚀 开始akshare数据获取任务...")
    print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查当前数据状态
    print_data_status()
    
    # 询问是否继续
    response = input("\n是否继续获取最新数据？(y/n): ").lower().strip()
    if response != 'y':
        print(">>> 用户取消操作")
        return
    
    success_count = 0
    
    # 获取概念数据
    if fetch_ak_concepts_and_save():
        success_count += 1
    
    # 稍作休息
    print(">>> 休息5秒后继续...")
    time.sleep(5)
    
    # 获取行业数据
    if fetch_ak_industries_and_save():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("🎉 akshare数据获取任务完成")
    print("=" * 60)
    print(f"完成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"成功任务: {success_count}/2")
    
    if success_count == 2:
        print("✅ 所有数据获取成功，可以在主程序中使用")
    else:
        print("⚠️  部分数据获取失败，请检查网络连接和akshare状态")

if __name__ == "__main__":
    main()
