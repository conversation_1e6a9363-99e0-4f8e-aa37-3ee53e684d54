核心目的:
该文件是一个专用的 数据查询工具库，旨在为其他分析脚本（如 dynamic_gap_detector.py）提供一个统一、安全、简洁的数据访问接口。它封装了所有与 fund_flow.db 和 fund_flow_minute.db 数据库的交互细节。
核心设计原则:
模块化: 将数据查询逻辑与数据分析逻辑分离，使代码更清晰、更易于维护。
回测安全 (Backtest Safety): 这是最重要的原则。所有查询函数都内置了严格的时间点控制机制，通过接收 query_date 和 backtest_time 参数，确保在任何回测时间点上，绝对不会获取到未来的数据，从而彻底杜绝“未来函数”问题。
接口统一: 无论底层数据结构如何，所有函数都返回标准化的 Pandas DataFrame 格式，便于进行后续的数据处理和分析。
易于使用: 调用者无需了解SQL或数据库的具体表结构，只需调用简单的Python函数即可获取所需数据。
2. 数据源总览
该工具库主要从 fund_flow.py 生成的两个数据库文件中读取数据。
数据库 1: fund_flow_minute.db (分钟级数据)
表名: minute_fund_flow
核心字段: stock_code, date, time, main_net_inflow, super_large_net_inflow 等。
数据内容: 记录了每只股票在交易日内每一分钟的资金流入流出情况。
回测关键: date 和 time 字段是防止未来函数的核心。
数据库 2: fund_flow.db (日度及统计数据)
表名: daily_fund_flow
数据内容: 每日收盘后的个股资金流完整数据，是所有日度计算的基础。
回测关键: date 字段。回测 2025-07-29 时，最多只能获取 2025-07-29 当天的数据。
表名: daily_rankings
数据内容: 每日根据不同指标（如主力净流入、超大单净流入等）生成的股票排行榜。
回测关键: date 字段。
表名: historical_max_rankings
数据内容: 记录了在特定日期，哪些股票的资金流指标当天创下了历史新高。
回测关键: date 字段。
表名: stock_stats_up, stock_stats_down, stock_stats_extremes 等
数据内容: 基于历史数据计算出的统计指标，例如某只股票在所有上涨日/下跌日的平均资金表现。
回测关键: date 字段。这个date代表统计计算完成的日期。回测时，只能获取不晚于当前回测日期的最新一份统计报告。
3. 函数 API 详解
query_minute_inflow(stock_codes, query_date, backtest_time=None)
功能: 查询指定股票在某一天特定时间点之前的分钟级资金流入数据。
参数:
stock_codes (list): 股票代码的列表。例如 ['600570', '002230']。
query_date (str): 查询的日期，格式为 'YYYY-MM-DD'。
backtest_time (str, 可选): 回测安全的关键参数。格式为 'HH:MM:SS'。如果提供此参数，函数将只返回 time 字段小于或等于此时间点的所有记录。如果为 None，则返回当天的全部数据。
返回值: 一个 Pandas DataFrame，包含以下列：stock_code, time, main_net_inflow, super_large_net_inflow, large_net_inflow。如果无数据，则返回一个空的DataFrame。
query_daily_ranking(query_date, metric='main_net_inflow', top_n=20)
功能: 获取指定日期的资金流排行榜。
参数:
query_date (str): 回测安全的关键参数。查询的日期，格式为 'YYYY-MM-DD'。回测时应传入当前回测的日期。
metric (str, 可选): 排行榜的指标。默认为 'main_net_inflow'。可选值参考 fund_flow.py 中的 RANKING_METRICS 列表。
top_n (int, 可选): 获取排行榜的前 N 名，默认为 20。
返回值: 一个 Pandas DataFrame，包含以下列：rank_position, stock_code, stock_name, metric_value。
query_historical_high_ranking(query_date, metric='main_net_inflow', top_n=20)
功能: 获取指定日期当天创下资金流历史新高的股票列表。
参数:
query_date (str): 回测安全的关键参数。查询的日期。
metric (str, 可选): 指标名称，默认为 'main_net_inflow'。
top_n (int, 可选): 获取排名前 N 名，默认为 20。
返回值: 一个 Pandas DataFrame，包含以下列：stock_code, current_value (当日创纪录的值), historical_max_date (上一个历史最高记录的日期)。
query_stock_stats(stock_codes, query_date)
功能: 查询一只或多只股票的历史统计特征（如上涨/下跌日均表现、历史极值等）。
回测安全机制: 此函数会自动在数据库中查找不晚于 query_date 的最新一份统计报告。
参数:
stock_codes (list): 股票代码的列表。
query_date (str): 回测安全的关键参数。查询的基准日期。
返回值: 一个字典。键是统计表的名称（如 'stats_up', 'stats_down', 'extremes'），值是包含对应数据的 Pandas DataFrame。
query_historical_max_value(stock_codes, query_date, metric='main_net_inflow')
功能: 查询指定股票在某一日之前的历史最大资金流入值。这是实现“突破历史新高买入”策略的关键函数。
回测安全机制: 函数通过 WHERE date < ? 条件，严格查询 query_date 之前的所有数据来进行计算，从源头上保证了在 query_date 当天做决策时，使用的是纯粹的历史数据。
参数:
stock_codes (list): 股票代码的列表。
query_date (str): 回测安全的关键参数。查询的基准日期，函数将返回此日期之前的历史最大值。
metric (str, 可选): 需要查询的指标列名。默认为 'main_net_inflow'。函数内置了安全白名单，只允许查询 daily_fund_flow 表中存在的数值列。
返回值: 一个 Pandas DataFrame，包含三列：stock_code, metric, historical_max_value。如果某只股票在 query_date 之前没有任何历史数据，其 historical_max_value 将为 None。
4. 调用示例（在 dynamic_gap_detector.py 中）
以下代码片段展示了如何在 dynamic_gap_detector.py 的主回测循环中，安全地调用这些函数来获取数据。
Generated python
# dynamic_gap_detector.py

# 1. 在文件顶部导入工具库
import fund_flow_query_util as fq
from datetime import datetime

# 2. 在配置区定义需要查询的股票
BACKTEST_DATE = '2025-07-29'
STOCKS_TO_MONITOR_MINUTE = ['600570', '002230'] # 监控分钟数据
STOCKS_TO_QUERY_STATS = ['600570']              # 查询历史统计
STOCKS_FOR_HIST_MAX_CHECK = ['600570', '002230'] # 需要检查历史最大值的股票

# 3. 在主回测循环中调用
def _run_analysis_core(date_str):
    # ... (前面的代码)

    # 假设这是回测的时间循环
    # for ts_str in timestamps:
    #     current_sim_time = datetime.strptime(ts_str, '%H%M%S').time()
    
    # --- 模拟一个时间点 ---
    current_sim_time = datetime.strptime("10:15:30", "%H:%M:%S").time()
    print(f"\n{'='*20} 当前回测时间点: {current_sim_time} {'='*20}")

    try:
        # --- 示例1: 查询分钟数据 ---
        print("\n--- [查询] 分钟资金流入 ---")
        minute_df = fq.query_minute_inflow(
            stock_codes=STOCKS_TO_MONITOR_MINUTE,
            query_date=date_str,
            backtest_time=current_sim_time.strftime('%H:%M:%S') # 传入当前时间，防止未来函数
        )
        if not minute_df.empty:
            print(f"成功获取 {len(minute_df)} 条分钟数据，显示最新5条:")
            print(minute_df.tail())

        # --- 示例2: 查询日度排行榜 ---
        print("\n--- [查询] 日度排行榜 (基于当日数据) ---")
        ranking_df = fq.query_daily_ranking(query_date=date_str, top_n=5)
        print(ranking_df)

        # --- 示例3: 查询个股历史统计数据 ---
        print("\n--- [查询] 个股历史统计 (基于不晚于今日的最新计算结果) ---")
        stats_data = fq.query_stock_stats(
            stock_codes=STOCKS_TO_QUERY_STATS,
            query_date=date_str # 函数内部会自动找到不晚于此日期的最新统计
        )
        for stat_name, stat_df in stats_data.items():
            print(f"\n--- 统计类型: {stat_name} ---")
            if not stat_df.empty:
                print(stat_df.to_string())

        # --- 示例4: 查询历史最大值，用于策略判断 ---
        print("\n--- [查询] 历史最大值 (用于今日策略判断) ---")
        metric_to_check = 'main_net_inflow'
        hist_max_df = fq.query_historical_max_value(
            stock_codes=STOCKS_FOR_HIST_MAX_CHECK,
            query_date=date_str, # 传入今天，函数会返回今天之前的数据
            metric=metric_to_check
        )
        if not hist_max_df.empty:
            print(f"查询到 {len(hist_max_df)} 只股票在 {date_str} 之前的 '{metric_to_check}' 历史最大值:")
            print(hist_max_df)
            # 策略应用示例:
            # for index, row in hist_max_df.iterrows():
            #     stock_code = row['stock_code']
            #     historical_max = row['historical_max_value']
            #     # 假设 current_inflow 是当天最新的资金流入值
            #     if current_inflow > historical_max:
            #         print(f"交易信号: {stock_code} 今日流入已超过历史最大值！")

    except Exception as e:
        print(f"数据查询时发生错误: {e}")

    # ... (接下来是您自己的断层分析等逻辑)
    # ...

# 运行主函数
# _run_analysis_core(BACKTEST_DATE)