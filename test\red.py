import os
import logging
from struct import unpack
import pandas as pd
import akshare
from mootdx.quotes import Quotes
import time
from functools import wraps
import sqlite3
from datetime import datetime, timedelta, date
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 通达信数据路径配置
TDX_DIR = r'E:\new_tdx'  # 通达信安装根目录
PATH = os.path.join(TDX_DIR, 'T0002', 'hq_cache')

# 数据库文件名
DB_FILE = 'stock_block_analysis.db'


# 日期适配器
def adapt_date(d):
    return d.isoformat()

def convert_date(s):
    return date.fromisoformat(s.decode())

sqlite3.register_adapter(date, adapt_date)
sqlite3.register_converter("date", convert_date)

def get_db_connection():
    return sqlite3.connect(DB_FILE, detect_types=sqlite3.PARSE_DECLTYPES)

# 重试装饰器
def retry(max_attempts=3, delay=2):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempts = 0
            while attempts < max_attempts:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    attempts += 1
                    logger.warning(f"尝试 {attempts}/{max_attempts} 失败: {str(e)}")
                    if attempts == max_attempts:
                        logger.error(f"达到最大重试次数。最后一次错误: {str(e)}")
                        raise
                    time.sleep(delay)
        return wrapper
    return decorator

def read_cfg_file(file_name):
    """读取配置文件，如果文件不存在则返回空列表"""
    file_path = os.path.join(PATH, file_name)
    try:
        with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
            return [line.strip().split('|') for line in f if line.strip()]
    except FileNotFoundError:
        logger.warning(f"配置文件未找到: {file_path}")
        return []
    except Exception as e:
        logger.error(f"读取配置文件 {file_path} 失败: {e}")
        return []


def get_block_file(block_type):
    file_name = f'block_{block_type}.dat'
    file_path = os.path.join(PATH, file_name)
    logger.info(f"读取文件: {file_path}")
    try:
        with open(file_path, 'rb') as f:
            buff = f.read()
    except FileNotFoundError:
        logger.error(f"文件未找到: {file_path}")
        return []

    head = unpack('<384sh', buff[:386])
    blk = buff[386:]
    blocks = [blk[i * 2813:(i + 1) * 2813] for i in range(head[1])]
    bk_list = []
    for bk in blocks:
        name = bk[:9].decode('gbk', errors='ignore').strip('\x00')
        num, category = unpack('<2h', bk[9:13])
        stocks = bk[13:(13 + 7 * num)].decode('gbk', errors='ignore').split('\x00')
        bk_list.append({'name': name, 'type': block_type, 'stocks': stocks})
    return bk_list


def get_stock_hyblock_tdx_loc():
    """获取股票行业板块配置，如果文件不存在则返回空DataFrame"""
    buf_line = read_cfg_file('tdxhy.cfg')
    if not buf_line:
        return pd.DataFrame(columns=['code', 'block', 'block5'])

    buf_lis = []
    mapping = {'0': 'sz', '1': 'sh', '2': 'bj'}
    for x in buf_line:
        if len(x) >= 3:
            x[1] = f"{mapping.get(x[0], '')}{x[1]}"
            buf_lis.append(x)

    if not buf_lis:
        return pd.DataFrame(columns=['code', 'block', 'block5'])

    df = pd.DataFrame(buf_lis, columns=['c0', 'code', 'block', 'c1', 'c2', 'c3'])
    df = df[['code', 'block']]
    df = df[df['block'] != '']
    df['block5'] = df['block'].str[0:5]
    return df


def get_block_zs_tdx_loc(block='hy'):
    """获取板块指数配置，如果文件不存在则返回空DataFrame"""
    buf_line = read_cfg_file('tdxzs3.cfg')
    if not buf_line:
        return pd.DataFrame(columns=['name', 'block'])

    mapping = {'hy': '2', 'dq': '3', 'gn': '4', 'fg': '5', 'yjhy': '12', 'zs': '6'}
    df = pd.DataFrame(buf_line, columns=['name', 'code', 'type', 't1', 't2', 'block'])
    df = df[df['type'] == mapping[block]]
    return df[['name', 'block']]


def get_industry_blocks_from_akshare():
    """使用akshare获取行业板块数据"""
    try:
        logger.info("使用akshare获取行业板块数据")

        # 随机延时1-3秒避免被限制
        delay = random.uniform(1, 3)
        logger.debug(f"获取行业板块列表前延时 {delay:.2f} 秒")
        time.sleep(delay)

        # 获取行业板块名称列表
        industry_df = akshare.stock_board_industry_name_em()
        industry_blocks = []

        # 限制处理前5个行业板块，避免请求过多
        for idx, (_, industry) in enumerate(industry_df.head(5).iterrows(), 1):
            industry_name = industry['板块名称']
            logger.info(f"正在处理行业板块 {idx}/5: {industry_name}")

            try:
                # 获取行业板块成分股
                stocks_df = akshare.stock_board_industry_cons_em(symbol=industry_name)
                stock_codes = stocks_df['代码'].tolist() if not stocks_df.empty else []

                industry_blocks.append({
                    'name': industry_name,
                    'type': '行业板块',
                    'stocks': stock_codes
                })

                # 随机延时1-3秒避免被限制
                delay = random.uniform(1, 3)
                logger.debug(f"延时 {delay:.2f} 秒")
                time.sleep(delay)
            except Exception as e:
                logger.warning(f"获取行业板块 {industry_name} 成分股失败: {e}")
                continue

        logger.info(f"成功获取 {len(industry_blocks)} 个行业板块")
        return industry_blocks
    except Exception as e:
        logger.error(f"使用akshare获取行业板块失败: {e}")
        return []


def get_industry_blocks():
    """获取行业板块信息，优先使用TDX数据，如果不可用则使用akshare数据"""
    try:
        logger.info("开始获取行业板块信息...")
        # 首先尝试使用TDX数据
        stocklist = get_stock_hyblock_tdx_loc()
        blocklist = get_block_zs_tdx_loc('hy')

        logger.info(f"stocklist shape: {stocklist.shape if not stocklist.empty else 'empty'}")
        logger.info(f"blocklist shape: {blocklist.shape if not blocklist.empty else 'empty'}")

        if not stocklist.empty and not blocklist.empty:
            logger.info("使用TDX数据获取行业板块")
            industry_blocks = []
            for _, block in blocklist.iterrows():
                block_stocks = stocklist[stocklist['block'].str.startswith(block['block'])]['code'].tolist()
                industry_blocks.append({
                    'name': block['name'],
                    'type': '行业板块',
                    'stocks': block_stocks
                })
            logger.info(f"TDX获取到 {len(industry_blocks)} 个行业板块")
            return industry_blocks
        else:
            # TDX数据不可用，使用akshare获取行业板块数据
            logger.warning("TDX行业板块数据不可用，尝试使用akshare获取")
            return get_industry_blocks_from_akshare()
    except Exception as e:
        logger.error(f"获取行业板块失败: {e}")
        # 尝试备用方案
        try:
            return get_industry_blocks_from_akshare()
        except Exception as e2:
            logger.error(f"备用方案也失败: {e2}")
            return []


def get_east_concepts():
    """获取东方财富概念板块信息"""
    try:
        # 随机延时1-3秒避免被限制
        delay = random.uniform(1, 3)
        logger.debug(f"获取概念列表前延时 {delay:.2f} 秒")
        time.sleep(delay)

        # 使用akshare获取概念板块名称列表
        concepts_df = akshare.stock_board_concept_name_em()
        logger.info(f"获取到 {len(concepts_df)} 个东方财富概念")
        return concepts_df
    except Exception as e:
        logger.error(f"获取东方财富概念失败: {e}")
        return pd.DataFrame()


@retry(max_attempts=3, delay=2)
def get_concept_stocks(concept_code):
    """获取概念板块成分股"""
    try:
        # 随机延时1-3秒避免被限制
        delay = random.uniform(1, 3)
        logger.debug(f"获取概念股票前延时 {delay:.2f} 秒")
        time.sleep(delay)

        # 使用akshare获取概念板块成分股
        return akshare.stock_board_concept_cons_em(symbol=concept_code)
    except Exception as e:
        logger.error(f"获取概念代码 {concept_code} 的股票列表失败: {e}")
        raise


def init_database():
    if os.path.exists(DB_FILE):
        os.remove(DB_FILE)
        logger.info(f"已删除旧的数据库文件: {DB_FILE}")

    with get_db_connection() as conn:
        cur = conn.cursor()
        cur.executescript('''
        -- 股票表
        CREATE TABLE IF NOT EXISTS stocks (
            stock_code TEXT PRIMARY KEY,
            short_name TEXT,
            exchange TEXT,
            list_date DATE,
            style_blocks TEXT,
            concept_blocks TEXT,
            index_blocks TEXT,
            industry_blocks TEXT
        );

        -- 各类板块表
        CREATE TABLE IF NOT EXISTS style_blocks (
            block_id INTEGER PRIMARY KEY AUTOINCREMENT,
            block_name TEXT NOT NULL UNIQUE,
            stocks TEXT
        );
        CREATE TABLE IF NOT EXISTS concept_blocks (
            block_id INTEGER PRIMARY KEY AUTOINCREMENT,
            block_name TEXT NOT NULL,
            concept_code TEXT UNIQUE,
            source TEXT,
            stocks TEXT
        );
        CREATE TABLE IF NOT EXISTS index_blocks (
            block_id INTEGER PRIMARY KEY AUTOINCREMENT,
            block_name TEXT NOT NULL UNIQUE,
            stocks TEXT
        );
        CREATE TABLE IF NOT EXISTS industry_blocks (
            block_id INTEGER PRIMARY KEY AUTOINCREMENT,
            block_name TEXT NOT NULL UNIQUE,
            stocks TEXT
        );

        -- 股票-板块关系表
        CREATE TABLE IF NOT EXISTS stock_block_relation (
            stock_code TEXT,
            block_id INTEGER,
            block_type TEXT,
            FOREIGN KEY (stock_code) REFERENCES stocks (stock_code),
            PRIMARY KEY (stock_code, block_id, block_type)
        );

        -- 创建索引以提高查询性能
        CREATE INDEX IF NOT EXISTS idx_stocks_exchange ON stocks (exchange);
        CREATE INDEX IF NOT EXISTS idx_stock_block_relation_stock_code ON stock_block_relation (stock_code);
        CREATE INDEX IF NOT EXISTS idx_stock_block_relation_block_id ON stock_block_relation (block_id);
        CREATE INDEX IF NOT EXISTS idx_stock_block_relation_block_type ON stock_block_relation (block_type);

        -- 元数据表
        CREATE TABLE IF NOT EXISTS metadata (
            key TEXT PRIMARY KEY,
            value TEXT
        );
        ''')
    logger.info("数据库初始化完成")

def save_to_database(stock_info, block_results, east_concepts):
    with get_db_connection() as conn:
        cur = conn.cursor()

        # 清空旧数据
        cur.execute("DELETE FROM stock_block_relation")
        cur.execute("DELETE FROM stocks")
        for table in ['style_blocks', 'concept_blocks', 'index_blocks', 'industry_blocks']:
            cur.execute(f"DELETE FROM {table}")

        # 插入股票基本信息
        for _, row in stock_info.iterrows():
            # 根据股票代码判断交易所
            stock_code = row['代码']
            stock_name = row['名称']
            if stock_code.startswith('6'):
                exchange = 'SH'
            elif stock_code.startswith(('0', '3')):
                exchange = 'SZ'
            elif stock_code.startswith('8') or stock_code.startswith('4'):
                exchange = 'BJ'
            else:
                exchange = 'UNKNOWN'

            cur.execute('''
            INSERT INTO stocks (stock_code, short_name, exchange, list_date)
            VALUES (?, ?, ?, ?)
            ''', (stock_code, stock_name, exchange, ''))

        # 插入通达信板块数据并建立与股票的关系
        for block_type, blocks in block_results.items():
            table_name = {
                '风格板块': 'style_blocks',
                '概念板块': 'concept_blocks',
                '指数板块': 'index_blocks',
                '行业板块': 'industry_blocks'
            }[block_type]

            for block in blocks:
                # 使用INSERT OR IGNORE防止重复插入
                cur.execute(f'INSERT OR IGNORE INTO {table_name} (block_name) VALUES (?)', (block['name'],))
                # 获取block_id
                cur.execute(f'SELECT block_id FROM {table_name} WHERE block_name = ?', (block['name'],))
                result = cur.fetchone()
                block_id = result[0] if result else None

                if block_id is None:
                    logger.warning(f"未能获取到板块ID: {block['name']}")
                    continue

                for stock in block['stocks']:
                    cur.execute('''
                    INSERT OR IGNORE INTO stock_block_relation (stock_code, block_id, block_type)
                    VALUES (?, ?, ?)
                    ''', (stock, block_id, block_type))

        # 插入东方财富概念数据
        total_concepts = len(east_concepts)
        for idx, (_, concept) in enumerate(east_concepts.iterrows(), 1):
            concept_name = concept['板块名称']
            concept_code = concept['板块代码']
            logger.info(f"正在处理东方财富概念 {idx}/{total_concepts}: {concept_name}")
            cur.execute('''
            INSERT OR REPLACE INTO concept_blocks (block_name, concept_code, source)
            VALUES (?, ?, ?)
            ''', (concept_name, concept_code, '东方财富'))
            # 获取block_id
            cur.execute('SELECT block_id FROM concept_blocks WHERE concept_code = ?', (concept_code,))
            result = cur.fetchone()
            block_id = result[0] if result else None

            if block_id is None:
                logger.warning(f"未能获取到概念板块ID: {concept_name}")
                continue

            try:
                stocks_df = get_concept_stocks(concept_code)
            except Exception as e:
                logger.error(f"获取概念 {concept_name} 的股票列表时出错: {e}")
                continue  # 跳过出错的概念

            for _, stock in stocks_df.iterrows():
                # 根据akshare返回的列名调整
                stock_code = stock.get('代码', stock.get('stock_code', ''))
                if stock_code:
                    cur.execute('''
                    INSERT OR IGNORE INTO stock_block_relation (stock_code, block_id, block_type)
                    VALUES (?, ?, ?)
                    ''', (stock_code, block_id, '概念板块'))

            # 随机延时1-3秒避免被限制
            delay = random.uniform(1, 3)
            logger.debug(f"延时 {delay:.2f} 秒")
            time.sleep(delay)

        # 更新stocks表，存储板块信息
        block_types = ['风格板块', '概念板块', '指数板块', '行业板块']
        for block_type in block_types:
            table_name = {
                '风格板块': 'style_blocks',
                '概念板块': 'concept_blocks',
                '指数板块': 'index_blocks',
                '行业板块': 'industry_blocks'
            }[block_type]

            column_name = {
                '风格板块': 'style_blocks',
                '概念板块': 'concept_blocks',
                '指数板块': 'index_blocks',
                '行业板块': 'industry_blocks'
            }[block_type]

            logger.info(f"正在更新股票的{block_type}信息到stocks表的{column_name}字段")

            # 更新stocks表
            cur.execute(f'''
            UPDATE stocks
            SET {column_name} = (
                SELECT GROUP_CONCAT(b.block_name)
                FROM stock_block_relation sbr
                JOIN {table_name} b ON sbr.block_id = b.block_id
                WHERE sbr.stock_code = stocks.stock_code AND sbr.block_type = ?
            )
            WHERE EXISTS (
                SELECT 1 FROM stock_block_relation sbr
                WHERE sbr.stock_code = stocks.stock_code AND sbr.block_type = ?
            )
            ''', (block_type, block_type))

        # 更新板块表的stocks字段，存储对应的股票列表
        for block_type in block_types:
            table_name = {
                '风格板块': 'style_blocks',
                '概念板块': 'concept_blocks',
                '指数板块': 'index_blocks',
                '行业板块': 'industry_blocks'
            }[block_type]

            logger.info(f"正在更新{block_type}表的stocks字段")

            # 更新板块表
            cur.execute(f'''
            UPDATE {table_name}
            SET stocks = (
                SELECT GROUP_CONCAT(s.stock_code)
                FROM stock_block_relation sbr
                JOIN stocks s ON sbr.stock_code = s.stock_code
                WHERE sbr.block_id = {table_name}.block_id AND sbr.block_type = ?
            )
            WHERE EXISTS (
                SELECT 1 FROM stock_block_relation sbr
                WHERE sbr.block_id = {table_name}.block_id AND sbr.block_type = ?
            )
            ''', (block_type, block_type))

        conn.commit()
        logger.info("数据已保存到数据库")



def query_stock_info(stock_code):
    with sqlite3.connect(DB_FILE) as conn:
        cur = conn.cursor()
        cur.execute('''
        SELECT stock_code, short_name, exchange, list_date, 
               style_blocks, concept_blocks, index_blocks, industry_blocks
        FROM stocks
        WHERE stock_code = ?
        ''', (stock_code,))
        return cur.fetchone()


def query_block_stocks(block_name):
    with sqlite3.connect(DB_FILE) as conn:
        cur = conn.cursor()
        # 查询风格板块
        cur.execute('''
        SELECT stocks FROM style_blocks WHERE block_name = ?
        ''', (block_name,))
        result = cur.fetchone()
        if result and result[0]:
            stocks = result[0].split(',')
            return stocks

        # 查询概念板块
        cur.execute('''
        SELECT stocks FROM concept_blocks WHERE block_name = ?
        ''', (block_name,))
        result = cur.fetchone()
        if result and result[0]:
            stocks = result[0].split(',')
            return stocks

        # 查询指数板块
        cur.execute('''
        SELECT stocks FROM index_blocks WHERE block_name = ?
        ''', (block_name,))
        result = cur.fetchone()
        if result and result[0]:
            stocks = result[0].split(',')
            return stocks

        # 查询行业板块
        cur.execute('''
        SELECT stocks FROM industry_blocks WHERE block_name = ?
        ''', (block_name,))
        result = cur.fetchone()
        if result and result[0]:
            stocks = result[0].split(',')
            return stocks

        return []



def get_all_stocks():
    """获取所有A股股票列表"""
    try:
        # 随机延时1-3秒避免被限制
        delay = random.uniform(1, 3)
        logger.debug(f"获取股票列表前延时 {delay:.2f} 秒")
        time.sleep(delay)

        # 使用akshare获取A股实时行情数据，包含股票代码和名称
        stock_info = akshare.stock_zh_a_spot_em()
        return stock_info[['代码', '名称']]
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        return pd.DataFrame(columns=['代码', '名称'])


def analyze_blocks():
    logger.info("开始分析板块数据...")

    # 获取所有股票信息
    stock_info = get_all_stocks()
    logger.info(f"获取到 {len(stock_info)} 只股票的基本信息")

    # 获取通达信板块数据
    block_results = {
        '风格板块': get_block_file('fg'),
        '概念板块': get_block_file('gn'),
        '指数板块': get_block_file('zs'),
        '行业板块': get_industry_blocks()
    }

    # 获取东方财富概念数据
    east_concepts = get_east_concepts()

    logger.info("\n=== 板块分析结果 ===")
    for category, blocks in block_results.items():
        logger.info(f"\n{category}:")
        for block in blocks:
            if not block['stocks']:
                logger.warning(f"  板块 {block['name']} 没有有效的股票数据")
                continue

            logger.info(f"  板块名称: {block['name']}")
            logger.info(f"  股票数量: {len(block['stocks'])}")
            logger.info(f"  股票列表: {', '.join(block['stocks'][:5])}...")
            logger.info("  ---")

    save_to_database(stock_info, block_results, east_concepts)
    logger.info("板块数据分析完成并保存到数据库")

def main():
    init_database()
    analyze_blocks()

    # 示例查询
    stock_code = "000001"
    stock_info = query_stock_info(stock_code)
    if stock_info:
        logger.info(f"\n股票 {stock_code} 信息:")
        logger.info(f"  名称: {stock_info[1]}")
        logger.info(f"  交易所: {stock_info[2]}")
        logger.info(f"  上市日期: {stock_info[3]}")
        logger.info(f"  风格板块: {stock_info[4]}")
        logger.info(f"  概念板块: {stock_info[5]}")
        logger.info(f"  指数板块: {stock_info[6]}")
        logger.info(f"  行业板块: {stock_info[7]}")
    else:
        logger.info(f"\n未找到股票 {stock_code} 的信息")

    block_name = "融资融券"
    stocks = query_block_stocks(block_name)
    if stocks:
        logger.info(f"\n板块 {block_name} 包含的股票:")
        for stock_code in stocks[:10]:
            # 获取股票名称
            cur = get_db_connection().cursor()
            cur.execute('SELECT short_name FROM stocks WHERE stock_code = ?', (stock_code,))
            result = cur.fetchone()
            short_name = result[0] if result else ''
            logger.info(f"  {stock_code}: {short_name}")
        if len(stocks) > 10:
            logger.info(f"  ... 共 {len(stocks)} 只股票")
    else:
        logger.info(f"\n未找到板块 {block_name} 的股票信息")


if __name__ == "__main__":
    main()
