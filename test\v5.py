import pandas as pd
import numpy as np
import os
import re
import sys
from datetime import datetime
from tabulate import tabulate
import warnings

warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# ——— 1. 配置区域 ———
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'
BACKTEST_DATE = '2025-07-25'

# V6 - 断层分析核心参数 (双重确认法)
# --------------------------------------------------
GAP_MIN_LEAD_THRESHOLD = 1.5
GAP_CONTEXT_MULTIPLIER = 1.2

# V6 - 资金加速度分析核心参数
# --------------------------------------------------
ACC_MIN_DELTA_THRESHOLD = 1.5e8
ACC_MIN_RATIO_THRESHOLD = 1.3
ACC_MIN_INFLOW_THRESHOLD = 3e8

# --- 通用配置 ---
ANALYSIS_TOP_N = 10
MIN_SECTORS_FOR_ANALYSIS = 4

# --- 全局状态变量，用于存储上一分钟的数据快照 ---
previous_data_snapshot = {}


class LogCapture:
    """日志捕获类，用于将print输出同时保存到文件"""
    def __init__(self, log_file_path):
        self.log_file_path = log_file_path
        self.original_stdout = sys.stdout
        self.log_file = None

    def __enter__(self):
        self.log_file = open(self.log_file_path, 'w', encoding='utf-8')
        sys.stdout = self
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.original_stdout
        if self.log_file:
            self.log_file.close()

    def write(self, text):
        # 同时写入控制台和文件
        self.original_stdout.write(text)
        if self.log_file:
            self.log_file.write(text)
            self.log_file.flush()

    def flush(self):
        self.original_stdout.flush()
        if self.log_file:
            self.log_file.flush()


def format_amount(amount):
    if amount is None or not isinstance(amount, (int, float)): return 'N/A'
    if abs(amount) >= 1e8: return f"{amount / 1e8:.2f}亿"
    if abs(amount) >= 1e4: return f"{amount / 1e4:.2f}万"
    return f"{amount:.2f}"


def convert_to_float(value):
    if isinstance(value, str):
        try:
            return float(value.replace('%', '').replace(',', ''))
        except (ValueError, TypeError):
            return 0.0
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0


def extract_timestamp_from_filename(filename):
    """从文件名中提取时间戳，支持多种格式"""
    # 格式1: HH-MM_ (旧格式) 例如: 09-30_zt_pool.csv
    match = re.search(r'^(\d{2})-(\d{2})_', filename)
    if match:
        hour, minute = match.groups()
        return f"{hour}{minute}00"
    
    # 格式2: _YYYYMMDD_HHMMSS (新格式) 例如: fund_flow_rank_20250725_093047.csv
    match = re.search(r'_\d{8}_(\d{2})(\d{2})(\d{2})\.', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"
    
    # 格式3: _HHMMSS.csv (新格式) 例如: 涨停股池_akshare_东方财富_093651.csv
    match = re.search(r'_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"
    
    return None


def classify_file_type(filename):
    """根据文件名分类文件类型，支持多种格式"""
    # 个股资金流文件匹配（移除main_fund_flow_）
    stock_flow_patterns = [
        'fund_flow_rank_',        # 个股资金流排名
        'fund_flow_tpdog.csv',    # tpdog个股资金流
        'ths_fund_flow.csv',      # 同花顺个股资金流  
        'fund_flow_akshare.csv',  # akshare个股资金流
        'individual_fund_flow_'   # 个股资金流排名
    ]
    
    # 概念资金流文件匹配
    concept_patterns = [
        'concept_fund_flow_tpdog.csv',
        'concept_fund_flow_akshare.csv', 
        'concept_fund_flow_'
    ]
    
    # 行业资金流文件匹配  
    sector_patterns = [
        'sector_fund_flow_tpdog.csv',
        'sector_fund_flow_akshare.csv',
        'sector_fund_flow_rank_'
    ]
    
    # 按长度降序排序，长模式优先匹配
    all_patterns = []
    for pattern in stock_flow_patterns:
        all_patterns.append((pattern, 'stock_flow'))
    for pattern in concept_patterns:
        all_patterns.append((pattern, 'concept'))
    for pattern in sector_patterns:
        all_patterns.append((pattern, 'sector'))
    
    all_patterns.sort(key=lambda x: len(x[0]), reverse=True)
    
    # 逐一匹配模式
    for pattern, file_type in all_patterns:
        if pattern in filename:
            return file_type
    
    return 'other'


def find_latest_file(file_list, current_time):
    relevant_file = None
    best_timestamp = None
    
    # 处理所有文件，不要早退出
    for f in sorted(file_list):
        try:
            timestamp = extract_timestamp_from_filename(f)
            if not timestamp: continue
            f_ts = datetime.strptime(timestamp, '%H%M%S').time()
            if f_ts <= current_time:
                # 如果时间戳更晚，或者时间戳相同但文件名更具体（包含日期），则优先选择
                if (best_timestamp is None or 
                    f_ts > best_timestamp or 
                    (f_ts == best_timestamp and '20250725' in f and (relevant_file is None or '20250725' not in relevant_file))):
                    relevant_file = f
                    best_timestamp = f_ts
        except (ValueError, IndexError):
            continue
    return relevant_file


def analyze_market_competition(inflows, names):
    """市场竞争激烈度分析器：检测是否存在明显龙头（完整版）"""
    
    if len(inflows) < 3:
        return {"is_competitive": False, "competition_type": "insufficient_data"}
    
    # 计算前几名的相对差距
    top5_ratios = []
    for i in range(min(5, len(inflows)-1)):
        if inflows[i+1] > 0:
            ratio = inflows[i] / inflows[i+1]
            top5_ratios.append(ratio)
    
    if not top5_ratios:
        return {"is_competitive": False, "competition_type": "no_data"}
    
    # 分析前几名的竞争情况
    top2_ratios = top5_ratios[:2] if len(top5_ratios) >= 2 else top5_ratios
    top3_ratios = top5_ratios[:3] if len(top5_ratios) >= 3 else top5_ratios
    
    # 各种竞争格局的检测
    is_highly_competitive = False
    competition_type = "clear_leader"
    leading_group = names[:1]
    reference_ratio = 0
    
    # 【优先检测】单龙头明显领先：如果第1名相比第2名有足够优势，直接返回非竞争
    if (len(top5_ratios) >= 1 and 
        top5_ratios[0] >= 1.12):  # 第1名领先≥12%，视为明显优势
        return {"is_competitive": False, "competition_type": "clear_leader"}
    
    # 1. 三足鼎立：前3名都非常接近
    if (len(top3_ratios) >= 2 and 
        all(r < 1.12 for r in top3_ratios[:2]) and  # 前3名都很接近
        max(top3_ratios[:2]) < 1.12):  # 最大差距也不大
        is_highly_competitive = True
        competition_type = "三足鼎立"
        leading_group = names[:3]
        reference_ratio = max(top3_ratios[:2])
    
    # 2. 双强争霸：前2名非常接近，但第3名有一定差距
    elif (len(top5_ratios) >= 1 and 
          top5_ratios[0] < 1.08 and  # 第1/第2名非常接近
          (len(top5_ratios) < 2 or top5_ratios[1] > 1.15)):  # 第3名有明显差距(降低阈值从1.20到1.15)
        is_highly_competitive = True
        competition_type = "双强争霸"
        leading_group = names[:2]
        reference_ratio = top5_ratios[0]
    
    # 3. 四强竞争：前4名都在同一水平线上（新增）
    elif (len(top5_ratios) >= 3 and
          all(r < 1.25 for r in top5_ratios[:3]) and  # 前4名差距都不太大
          sum(1 for r in top5_ratios[:3] if r < 1.15) >= 2):  # 至少2个位置差距很小
        is_highly_competitive = True
        competition_type = "多强竞争"
        leading_group = names[:4]
        reference_ratio = max(top5_ratios[:3])
    
    # 4. 竞争激烈：前2-3名比较接近
    elif (len(top2_ratios) > 0 and
          max(top2_ratios) < 1.15 and 
          (len(top2_ratios) < 2 or sum(top2_ratios)/len(top2_ratios) < 1.12)):
        is_highly_competitive = True
        competition_type = "竞争激烈"
        leading_group = names[:3]
        reference_ratio = max(top2_ratios) if top2_ratios else 0
    
    if is_highly_competitive:
        return {
            "is_competitive": True,
            "competition_type": competition_type,
            "leading_group": leading_group,
            "max_ratio": reference_ratio,
            "avg_ratio": sum(top2_ratios)/len(top2_ratios) if top2_ratios else reference_ratio
        }
    
    return {"is_competitive": False, "competition_type": "clear_leader"}


def analyze_market_state(inflows):
    inflows_yi = [x / 1e8 for x in inflows[:5]]
    total_top5 = sum(inflows_yi)
    
    # 资金规模分级
    if total_top5 > 80: scale = "超大资金"
    elif total_top5 > 40: scale = "大资金" 
    elif total_top5 > 15: scale = "中等资金"
    else: scale = "小资金"
    
    # 集中度计算 (前2名占前5名比例)
    concentration = sum(inflows_yi[:2]) / total_top5 if total_top5 > 0 else 0
    
    # 分散度计算 (变异系数)
    dispersion = np.std(inflows_yi) / np.mean(inflows_yi) if np.mean(inflows_yi) > 0 else 0
    
    return {
        "scale": scale,
        "concentration": concentration,
        "dispersion": dispersion,
        "total_top5": total_top5,
        "avg_top5": np.mean(inflows_yi)
    }


def find_all_gap_points(inflows):
    """全局断层点扫描器：找到所有可能的断层点（智能优先版）"""
    gap_scores = []
    
    for i in range(min(6, len(inflows)-1)):  # 只检查前6个位置
        abs_gap = inflows[i] - inflows[i+1]  # 绝对差距(元)
        rel_gap = inflows[i] / inflows[i+1] if inflows[i+1] > 0 else float('inf')  # 相对差距
        
        # 位置权重：前面的断层更重要
        if i == 0:  # 第1名后
            position_weight = 1.0
        elif i == 1:  # 第2名后
            position_weight = 0.67
        else:
            position_weight = 1.0 / (i/2 + 1)
        
        # 双龙头特殊处理：只有当前2名真正接近时才加权
        if i == 1 and len(inflows) >= 3:
            first_second_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else 1.0
            # 严格限制：只有相差<1.15倍才认为是真正的双龙头
            if first_second_ratio < 1.15:
                dual_leader_bonus = 2.0  # 提高加权倍数，但只对真正的双龙头生效
                position_weight *= dual_leader_bonus
        
        # 综合断层得分 = 绝对差距(亿) × 相对差距 × 位置权重
        gap_score = (abs_gap / 1e8) * rel_gap * position_weight
        
        gap_scores.append({
            "position": i,
            "abs_gap": abs_gap,
            "rel_gap": rel_gap,
            "score": gap_score,
            "position_weight": position_weight
        })
    
    # 智能断层点选择：优先考虑单龙头的可能性
    max_gap = max(gap_scores, key=lambda x: x["score"])
    
    # 单龙头优先检测：如果第1名后有明显优势，且第1名确实领先，优先选择第1名后
    if (len(gap_scores) >= 2 and 
        gap_scores[0]["rel_gap"] >= 1.15 and  # 第1名有明显领先
        gap_scores[0]["abs_gap"] >= 2e8 and   # 绝对差距也不小(≥2亿)
        gap_scores[0]["score"] >= max_gap["score"] * 0.65):  # 得分不能太低(≥最高分的65%)
        
        # 选择第1名后作为主要断层点
        max_gap = gap_scores[0]
    
    return gap_scores, max_gap


def identify_leading_group(inflows, gap_position, names):
    """集团识别器：识别领先集团并分析其特征（优化版）"""
    import numpy as np
    
    # 断层点前为领先集团
    leading_group = inflows[:gap_position+1]
    leading_names = names[:gap_position+1]
    following_group = inflows[gap_position+1:] if gap_position+1 < len(inflows) else []
    
    # 集团内部紧密度验证 - 针对双龙头优化
    if len(leading_group) > 1:
        internal_gaps = [leading_group[i]/leading_group[i+1] 
                        for i in range(len(leading_group)-1)]
        max_internal_gap = max(internal_gaps) if internal_gaps else 1.0
        
        # 双龙头特殊处理：如果集团只有2个成员且都是大额资金，放宽紧密度要求
        if len(leading_group) == 2:
            avg_amount = np.mean(leading_group) / 1e8  # 转换为亿元
            # 大资金双龙头（平均>10亿）：紧密度要求从1.4放宽到1.8
            if avg_amount > 10:
                cohesion_factor = 0.7  # 降低紧密度的惩罚力度
            else:
                cohesion_factor = 1.0
        else:
            cohesion_factor = 1.0
    else:
        max_internal_gap = 1.0
        cohesion_factor = 1.0
    
    return {
        "size": len(leading_group),
        "members": leading_names,
        "amounts": [x/1e8 for x in leading_group],  # 转换为亿元
        "avg_amount": np.mean(leading_group) / 1e8,
        "internal_cohesion": max_internal_gap,
        "cohesion_factor": cohesion_factor,  # 新增：用于调整紧密度评判标准
        "external_gap": leading_group[-1] / following_group[0] if following_group else float('inf')
    }


def calculate_dynamic_thresholds(market_state):
    """动态阈值计算器：根据市场状态自适应调整标准"""
    
    # 基础相对阈值根据资金规模调整
    scale_factors = {
        "超大资金": 1.15,  # 大资金市场门槛更低
        "大资金": 1.25,
        "中等资金": 1.35,
        "小资金": 1.45     # 小资金市场门槛更高
    }
    
    # 根据集中度微调：集中度越高，越容易形成断层
    concentration_adjustment = (1 - market_state["concentration"]) * 0.1
    
    dynamic_relative_threshold = scale_factors[market_state["scale"]] + concentration_adjustment
    
    # 绝对差距阈值：平均值的15-25%
    min_abs_gap_ratio = 0.15 + market_state["dispersion"] * 0.1
    dynamic_absolute_threshold = market_state["avg_top5"] * min_abs_gap_ratio
    
    return {
        "min_relative_gap": dynamic_relative_threshold,
        "min_absolute_gap": dynamic_absolute_threshold,
        "group_cohesion_limit": 1.4,  # 集团内部最大允许差距
        "min_gap_score": 1.5  # 降低最小综合得分要求 (从2.0降到1.5)
    }


def comprehensive_evaluation(market_state, max_gap, group_info, thresholds):
    """综合评判器：多维度智能评分（超大资金优化版）"""
    
    scores = {}
    
    # 1. 相对优势得分 (0-2分)
    rel_score = min(max_gap["rel_gap"] / thresholds["min_relative_gap"], 2.0)
    scores["relative"] = rel_score
    
    # 2. 绝对优势得分 (0-2分) - 针对超大资金市场优化
    abs_score = min((max_gap["abs_gap"]/1e8) / thresholds["min_absolute_gap"], 2.0)
    
    # 超大资金市场特殊处理：如果是明显的单龙头领先，给予绝对优势加分
    if (market_state["scale"] == "超大资金" and 
        max_gap["position"] == 0 and  # 第1名后断层
        max_gap["abs_gap"] >= 3e8 and  # 绝对差距≥3亿
        max_gap["rel_gap"] >= 1.15):   # 相对差距≥15%
        abs_score = min(abs_score * 1.8, 2.0)  # 绝对优势得分提升80%
    
    scores["absolute"] = abs_score
    
    # 3. 集团紧密度得分 (0-2分)：内部越紧密得分越高（优化版）
    effective_cohesion_limit = thresholds["group_cohesion_limit"] / group_info.get("cohesion_factor", 1.0)
    if group_info["internal_cohesion"] <= effective_cohesion_limit:
        cohesion_score = 2.0 - (group_info["internal_cohesion"] - 1.0) / (effective_cohesion_limit - 1.0)
    else:
        cohesion_score = 0.0
    scores["cohesion"] = max(cohesion_score, 0)
    
    # 4. 市场格局得分 (0-2分) - 针对双龙头优化
    concentration = market_state["concentration"]
    if group_info["size"] == 2 and concentration > 0.50:  # 双龙头且中等集中度以上
        pattern_score = 1.8  # 提高双龙头格局的得分
    elif concentration > 0.65:  # 高集中度
        pattern_score = 1.8
    elif concentration > 0.45:  # 中等集中度
        pattern_score = 1.2
    else:  # 分散市场
        pattern_score = 0.6
    scores["pattern"] = pattern_score
    
    # 5. 位置重要性得分 (0-2分)：越靠前的断层越重要
    position_score = 2.0 * max_gap["position_weight"] / 1.2  # 标准化权重
    scores["position"] = min(position_score, 2.0)
    
    # 6. 双龙头特殊加分 (0-0.5分)：识别双龙头格局的特殊奖励
    dual_leader_bonus = 0.0
    if (group_info["size"] == 2 and 
        max_gap["position"] == 1 and  # 第2名后断层
        group_info["avg_amount"] > 10):  # 大资金市场
        dual_leader_bonus = 0.3
    
    # 7. 单龙头特殊加分 (0-0.4分)：超大资金市场中的单龙头奖励（新增）
    single_leader_bonus = 0.0
    if (market_state["scale"] == "超大资金" and
        group_info["size"] == 1 and  # 单龙头
        max_gap["position"] == 0 and  # 第1名后断层
        max_gap["rel_gap"] >= 1.15):  # 有明显领先
        single_leader_bonus = 0.3
    
    scores["dual_leader_bonus"] = dual_leader_bonus
    scores["single_leader_bonus"] = single_leader_bonus
    
    # 综合得分 (加权平均 + 特殊加分)
    weights = {"relative": 0.22, "absolute": 0.22, "cohesion": 0.18, "pattern": 0.18, "position": 0.20}
    base_score = sum(scores[key] * weights[key] for key in weights)
    total_score = base_score + dual_leader_bonus + single_leader_bonus
    
    return total_score, scores


def find_sector_summary_file(sector_name, current_time, data_dir):
    """查找指定板块在指定时间的sector_summary文件"""
    try:
        all_files = os.listdir(data_dir)
        
        # 查找匹配的sector_summary文件
        sector_files = []
        for f in all_files:
            if f.startswith(f'sector_summary_{sector_name}_') and f.endswith('.csv'):
                sector_files.append(f)
        
        if not sector_files:
            return None
        
        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(sector_files, current_time)
        
    except Exception as e:
        print(f"查找sector_summary文件失败: {e}")
        return None


def parse_sector_internal_data(file_path):
    """解析sector_summary文件，返回个股资金流数据"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')
        
        # 检查必要的列是否存在
        required_cols = ['名称', '今日主力净流入-净额']
        if not all(col in df.columns for col in required_cols):
            print(f"sector_summary文件缺少必要列: {list(df.columns)}")
            return None
        
        # 转换数据类型
        df['今日主力净流入-净额'] = df['今日主力净流入-净额'].apply(convert_to_float)
        
        # 过滤正流入数据并排序
        positive_df = df[df['今日主力净流入-净额'] > 0].copy()
        positive_df = positive_df.sort_values(by='今日主力净流入-净额', ascending=False)
        
        return positive_df
        
    except Exception as e:
        print(f"解析sector_summary文件失败: {e}")
        return None


def parse_stock_flow_data(file_path, file_type):
    """解析个股资金流数据"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')

        # 定义需要的列映射
        required_columns = {
            '名称': ['名称', 'name', '股票名称'],
            '最新价': ['最新价', 'price', '现价'],
            '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
            '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
            '今日主力净流入-净占比': ['今日主力净流入-净占比', 'main_net_inflow_pct', '主力净流入占比'],
            '今日超大单净流入-净额': ['今日超大单净流入-净额', 'super_large_net_inflow', '超大单净流入'],
            '今日超大单净流入-净占比': ['今日超大单净流入-净占比', 'super_large_net_inflow_pct', '超大单净流入占比'],
            '今日大单净流入-净额': ['今日大单净流入-净额', 'large_net_inflow', '大单净流入'],
            '今日大单净流入-净占比': ['今日大单净流入-净占比', 'large_net_inflow_pct', '大单净流入占比'],
            '今日中单净流入-净额': ['今日中单净流入-净额', 'medium_net_inflow', '中单净流入'],
            '今日中单净流入-净占比': ['今日中单净流入-净占比', 'medium_net_inflow_pct', '中单净流入占比'],
            '今日小单净流入-净额': ['今日小单净流入-净额', 'small_net_inflow', '小单净流入'],
            '今日小单净流入-净占比': ['今日小单净流入-净占比', 'small_net_inflow_pct', '小单净流入占比']
        }

        # 查找并映射列名
        column_mapping = {}
        available_columns = []

        for target_col, possible_names in required_columns.items():
            for possible_name in possible_names:
                if possible_name in df.columns:
                    column_mapping[possible_name] = target_col
                    available_columns.append(target_col)
                    break

        # 至少需要名称和主力净流入
        if '名称' not in available_columns or '今日主力净流入-净额' not in available_columns:
            print(f"个股资金流文件缺少必要列: {file_path}")
            print(f"  可用列名: {list(df.columns)}")
            return None

        # 重命名列并选择可用列
        df_clean = df.rename(columns=column_mapping)
        df_clean = df_clean[available_columns].copy()

        # 转换数值列
        numeric_columns = [col for col in available_columns if col != '名称']
        for col in numeric_columns:
            df_clean[col] = df_clean[col].apply(convert_to_float)

        return df_clean

    except Exception as e:
        print(f"解析个股资金流文件失败: {e}")
        return None


# ==================== 个股资金流点火检测系统 ====================

class StockFlowIgnitionDetector:
    """个股资金流点火检测器 - 基于四大核心指标的主力点火信号识别"""

    def __init__(self):
        # 历史数据存储 - 用于计算变化率和排名变化
        self.historical_data = {}  # {timestamp: {stock_name: {rank, net_inflow, ...}}}
        self.previous_snapshot = None  # 上一个时间点的数据快照

        # 点火检测阈值配置
        self.ignition_thresholds = {
            'min_wra': 0.1,        # 最小排名权重加速度
            'min_ct': 10000,       # 最小资金冲击力(万元/分钟)
            'min_pf': 0.3,         # 最小主力纯度
            'min_rank_jump': 5,    # 最小排名跃迁
            'min_score': 5.0       # 最小综合评分
        }

    def calculate_rank_velocity(self, current_rank, previous_rank, time_delta_minutes=1):
        """计算排名速度 (Rank Velocity, RV)"""
        if previous_rank is None or current_rank is None:
            return 0
        return (previous_rank - current_rank) / time_delta_minutes

    def calculate_weighted_rank_acceleration(self, rank_velocity, current_rank):
        """计算排名权重加速度 (Weighted Rank Acceleration, WRA)"""
        if current_rank <= 0:
            return 0
        return rank_velocity * (1 / current_rank)

    def calculate_capital_thrust(self, current_inflow, previous_inflow, time_delta_minutes=1):
        """计算资金冲击力 (Capital Thrust, CT) - 单位：万元/分钟"""
        if previous_inflow is None or current_inflow is None:
            return 0
        return (current_inflow - previous_inflow) / 1e4 / time_delta_minutes

    def calculate_purity_of_force(self, super_large_inflow_change, total_inflow_change):
        """计算主力纯度 (Purity of Force, PF)"""
        if total_inflow_change <= 0:
            return 0
        return super_large_inflow_change / total_inflow_change

    def detect_ignition_signals(self, current_data, current_time):
        """检测主力点火信号"""
        ignition_signals = []

        if self.previous_snapshot is None:
            # 首次运行，保存当前数据作为基准
            self.previous_snapshot = self._create_data_snapshot(current_data, current_time)
            return []

        # 分析每只股票的点火信号
        for idx, stock in current_data.iterrows():
            if idx >= 200:  # 只分析前200名
                break

            stock_name = stock['名称']
            current_rank = idx + 1
            current_inflow = stock['今日主力净流入-净额']

            # 查找历史数据
            previous_data = self._find_previous_stock_data(stock_name)
            if previous_data is None:
                continue

            # 计算四大核心指标
            rv = self.calculate_rank_velocity(current_rank, previous_data['rank'])
            wra = self.calculate_weighted_rank_acceleration(rv, current_rank)
            ct = self.calculate_capital_thrust(current_inflow, previous_data['net_inflow'])

            # 主力纯度计算（需要超大单数据，暂时用净流入变化估算）
            # 假设大额资金流入的主力纯度较高
            if ct > 50000:  # 超过5万万元/分钟的大额流入
                pf = 0.8
            elif ct > 10000:  # 超过1万万元/分钟的中等流入
                pf = 0.6
            elif ct > 1000:   # 超过1000万元/分钟的小额流入
                pf = 0.4
            else:
                pf = 0.2

            # 检查是否满足点火条件
            if self._is_ignition_signal(wra, ct, pf, rv, current_rank, previous_data['rank']):
                signal = self._create_ignition_signal(
                    stock_name, stock, current_rank, previous_data,
                    wra, ct, pf, rv, current_time
                )
                ignition_signals.append(signal)

        # 更新历史数据
        self.previous_snapshot = self._create_data_snapshot(current_data, current_time)

        return ignition_signals

    def _is_ignition_signal(self, wra, ct, pf, rv, current_rank, previous_rank):
        """判断是否为点火信号"""
        # 基础条件检查
        if wra < self.ignition_thresholds['min_wra']:
            return False
        if ct < self.ignition_thresholds['min_ct']:
            return False
        if pf < self.ignition_thresholds['min_pf']:
            return False

        # 排名跃迁检查
        rank_jump = previous_rank - current_rank if previous_rank else 0
        if rank_jump < self.ignition_thresholds['min_rank_jump']:
            return False

        # 综合评分
        score = self._calculate_ignition_score(wra, ct, pf, rank_jump, current_rank)
        return score >= self.ignition_thresholds['min_score']

    def _calculate_ignition_score(self, wra, ct, pf, rank_jump, current_rank):
        """计算点火强度综合评分"""
        # WRA权重：40%
        wra_score = min(10, wra * 10) * 0.4

        # CT权重：30%
        ct_score = min(10, ct / 1000) * 0.3

        # PF权重：20%
        pf_score = pf * 10 * 0.2

        # 排名跃迁权重：10%
        jump_score = min(10, rank_jump / 50 * 10) * 0.1

        return wra_score + ct_score + pf_score + jump_score

    def _create_ignition_signal(self, stock_name, stock_data, current_rank, previous_data, wra, ct, pf, rv, current_time):
        """创建点火信号对象"""
        rank_jump = previous_data['rank'] - current_rank
        score = self._calculate_ignition_score(wra, ct, pf, rank_jump, current_rank)

        # 判断信号类型
        signal_type = self._classify_signal_type(current_time, ct, current_rank)

        # 价格响应计算（如果有涨跌幅数据）
        price_response = stock_data.get('今日涨跌幅', 0)

        return {
            'stock_name': stock_name,
            'stock_code': stock_data.get('代码', 'N/A'),
            'signal_type': signal_type,
            'time': current_time,
            'rank_jump': rank_jump,
            'old_rank': previous_data['rank'],
            'new_rank': current_rank,
            'wra': wra,
            'ct': ct,
            'pf': pf,
            'price_response': price_response,
            'score': score,
            'net_inflow': stock_data['今日主力净流入-净额']
        }

    def _classify_signal_type(self, current_time, ct, current_rank):
        """分类信号类型"""
        hour = current_time.hour if hasattr(current_time, 'hour') else 10

        if hour <= 10:
            return "盘中强攻型"
        elif hour >= 14:
            return "尾盘偷袭型"
        elif current_rank <= 10 and ct > 5000:
            return "龙头驱动型"
        else:
            return "盘中强攻型"

    def _create_data_snapshot(self, data, timestamp):
        """创建数据快照"""
        snapshot = {}
        for idx, stock in data.iterrows():
            if idx >= 200:  # 只保存前200名
                break
            snapshot[stock['名称']] = {
                'rank': idx + 1,
                'net_inflow': stock['今日主力净流入-净额'],
                'timestamp': timestamp
            }
        return snapshot

    def _find_previous_stock_data(self, stock_name):
        """查找股票的历史数据"""
        if self.previous_snapshot and stock_name in self.previous_snapshot:
            return self.previous_snapshot[stock_name]
        return None


def analyze_stock_flow_gap(df_stocks, current_time=None, data_dir=None):
    """个股资金流断层检测：基于板块内部分析的改进版 + 点火检测"""

    # 1. 数据预处理
    positive_flow_df = df_stocks[df_stocks['今日主力净流入-净额'] > 0]
    if len(positive_flow_df) < MIN_SECTORS_FOR_ANALYSIS:
        return f"【--- 个股资金流数据不足，无法分析 ---】"

    # 2. 点火信号检测
    ignition_detector = getattr(analyze_stock_flow_gap, '_ignition_detector', None)
    if ignition_detector is None:
        ignition_detector = StockFlowIgnitionDetector()
        analyze_stock_flow_gap._ignition_detector = ignition_detector

    ignition_signals = ignition_detector.detect_ignition_signals(positive_flow_df, current_time)

    # 3. 原有的断层检测逻辑
    # 取前50名进行分析
    ANALYSIS_TOP_N_STOCKS = 50
    inflows = positive_flow_df.head(ANALYSIS_TOP_N_STOCKS)['今日主力净流入-净额'].tolist()
    if len(inflows) < 2:
        return f"【--- 个股资金流数据不足，无法分析 ---】"

    names = positive_flow_df.head(ANALYSIS_TOP_N_STOCKS)['名称'].tolist()

    # 4. 竞争激烈度分析 - 优先检查
    competition_info = analyze_market_competition(inflows, names)

    # 5. 市场状态感知
    market_state = analyze_market_state(inflows)

    # 6. 全局断层点搜索
    gap_scores, max_gap = find_all_gap_points(inflows)

    # 7. 集团识别
    group_info = identify_leading_group(inflows, max_gap["position"], names)

    # 8. 动态阈值计算
    thresholds = calculate_dynamic_thresholds(market_state)

    # 9. 综合评判
    total_score, scores = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)

    # 10. 生成智能报告（包含点火信号）
    return generate_stock_flow_report_with_ignition(
        market_state, max_gap, group_info, thresholds, total_score, scores,
        competition_info, ignition_signals
    )


def generate_stock_flow_report(market_state, max_gap, group_info, thresholds, total_score, scores, competition_info=None):
    """个股资金流智能报告生成器"""
    
    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]
        
        return (f"【--- 个股资金流{competition_type}，无明显龙头 ---】\n"
                f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")
    
    # 判断是否发现断层
    is_gap_found = total_score >= thresholds["min_gap_score"]
    
    if not is_gap_found:
        # 未发现断层的报告
        leader_name = group_info["members"][0]
        return (f"【--- 个股资金流未发现显著资金断层 ---】\n"
                f"  当前龙头: 【{leader_name}】\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
    
    # 发现断层的详细报告
    if group_info["size"] == 1:
        gap_type = "个股单龙头断层"
        leader_desc = f"断层龙头: 【{group_info['members'][0]}】"
    else:
        gap_type = f"个股Top {group_info['size']} 领先集团"
        leader_desc = f"领先集团: 【{', '.join(group_info['members'])}】"
    
    # 断层分析描述
    gap_desc = (f"在第{max_gap['position']+1}名后发现显著断层，"
                f"绝对差距{max_gap['abs_gap']/1e8:.2f}亿元，"
                f"相对差距{max_gap['rel_gap']:.2f}倍")
    
    # 市场状态描述
    market_desc = (f"{market_state['scale']}市场 (总计{market_state['total_top5']:.1f}亿)，"
                   f"集中度{market_state['concentration']:.1%}")
    
    report_lines = [
        f"【★★★★★ 个股资金流发现资金断层! ★★★★★】",
        f"  格局类型: {gap_type}",
        f"  {leader_desc}",
        f"  断层分析: {gap_desc}",
        f"  市场状态: {market_desc}",
        f"  综合得分: {total_score:.2f}分 (超过{thresholds['min_gap_score']:.1f}分阈值)"
    ]
    
    return "\n".join(report_lines)


def generate_ignition_report(ignition_signals):
    """生成主力点火信号报告"""
    if not ignition_signals:
        return ""

    # 按评分排序，取前3个最强信号
    top_signals = sorted(ignition_signals, key=lambda x: x['score'], reverse=True)[:3]

    report_lines = ["【🔥🔥🔥 主力点火信号! 🔥🔥🔥】"]

    for i, signal in enumerate(top_signals, 1):
        stock_name = signal['stock_name']
        stock_code = signal['stock_code']
        signal_type = signal['signal_type']
        time_str = signal['time'].strftime('%H:%M:%S') if hasattr(signal['time'], 'strftime') else str(signal['time'])

        report_lines.append(f"  信号定性: {signal_type}")
        report_lines.append(f"  点火个股: 【{stock_name} ({stock_code})】 at {time_str}")
        report_lines.append("")
        report_lines.append("  核心引爆数据 (1分钟内):")
        report_lines.append(f"  - 排名跃迁 (WRA): {signal['old_rank']} -> {signal['new_rank']} (权重加速度: {signal['wra']:.3f})")
        report_lines.append(f"  - 资金脉冲 (CT):  新增净流入 {signal['ct']:.0f} 万元")
        report_lines.append(f"  - 主力纯度 (PF):  超大单贡献占比 {signal['pf']*100:.1f}%")
        report_lines.append(f"  - 价格响应:      股价瞬间拉升 {signal['price_response']:.2f}%")
        report_lines.append("")
        report_lines.append("  战场环境:")
        report_lines.append("  - 所属板块: [板块识别功能开发中]")
        report_lines.append("  - 板块热度: [板块状态分析中]")
        report_lines.append("")
        report_lines.append("  综合评估:")
        report_lines.append(f"  - 点火强度分: {signal['score']:.1f}/10 分")

        # 大师解读
        master_insight = generate_master_insight(signal)
        report_lines.append(f"  - 大师解读: {master_insight}")

        if i < len(top_signals):
            report_lines.append("")
            report_lines.append("-" * 50)

    return "\n".join(report_lines)


def generate_master_insight(signal):
    """生成大师解读"""
    score = signal['score']
    wra = signal['wra']
    ct = signal['ct']
    rank_jump = signal['rank_jump']

    if score >= 9:
        return f"超强主力重拳出击，排名暴涨{rank_jump}位，后续爆发力值得重点关注！"
    elif score >= 7:
        return f"明显资金集中流入，{ct:.0f}万资金快速推升，短期有望持续强势。"
    elif score >= 5:
        return f"资金开始聚集，排名提升{rank_jump}位，可关注后续资金接力情况。"
    else:
        return f"初现资金流入迹象，需观察是否有后续资金跟进。"


def generate_stock_flow_report_with_ignition(market_state, max_gap, group_info, thresholds, total_score, scores, competition_info=None, ignition_signals=None):
    """个股资金流智能报告生成器（包含点火信号）"""

    # 1. 首先显示点火信号（如果有）
    ignition_report = ""
    if ignition_signals:
        ignition_report = generate_ignition_report(ignition_signals) + "\n\n"

    # 2. 原有的断层检测报告
    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]

        gap_report = (f"【--- 个股资金流{competition_type}，无明显龙头 ---】\n"
                     f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                     f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                     f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")
        return ignition_report + gap_report

    # 判断是否发现断层
    is_gap_found = total_score >= thresholds["min_gap_score"]

    if not is_gap_found:
        # 未发现断层的报告
        leader_name = group_info["members"][0]
        gap_report = (f"【--- 个股资金流未发现显著资金断层 ---】\n"
                     f"  当前龙头: 【{leader_name}】\n"
                     f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                     f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
        return ignition_report + gap_report

    # 发现断层的详细报告
    if group_info["size"] == 1:
        gap_type = "个股单龙头断层"
        leader_desc = f"断层龙头: 【{group_info['members'][0]}】"
    else:
        gap_type = f"个股Top {group_info['size']} 领先集团"
        leader_desc = f"领先集团: 【{', '.join(group_info['members'])}】"

    # 断层分析描述
    gap_desc = (f"在第{max_gap['position']+1}名后发现显著断层，"
                f"绝对差距{max_gap['abs_gap']/1e8:.2f}亿元，"
                f"相对差距{max_gap['rel_gap']:.2f}倍")

    # 市场状态描述
    market_desc = (f"{market_state['scale']}市场 (总计{market_state['total_top5']:.1f}亿)，"
                   f"集中度{market_state['concentration']:.1%}")

    gap_report_lines = [
        f"【★★★★★ 个股资金流发现资金断层! ★★★★★】",
        f"  格局类型: {gap_type}",
        f"  {leader_desc}",
        f"  断层分析: {gap_desc}",
        f"  市场状态: {market_desc}",
        f"  综合得分: {total_score:.2f}分 (超过{thresholds['min_gap_score']:.1f}分阈值)"
    ]

    gap_report = "\n".join(gap_report_lines)
    return ignition_report + gap_report


def analyze_sector_internal_flow(leader_name, current_time, data_dir, sector_type, sector_total_amount):
    """分析断层龙头板块的内部个股资金流入情况"""
    
    if sector_type != '行业':  # 目前只支持行业板块
        return f"  \n--- {leader_name}内部个股分析 ---\n  概念板块内部数据暂不可用，功能开发中..."
    
    # 查找对应的sector_summary文件
    sector_file = find_sector_summary_file(leader_name, current_time, data_dir)
    
    if not sector_file:
        return f"  \n--- {leader_name}内部个股分析 ---\n  未找到对应的内部资金流数据文件"
    
    # 解析数据
    df_internal = parse_sector_internal_data(os.path.join(data_dir, sector_file))
    
    if df_internal is None or len(df_internal) == 0:
        return f"  \n--- {leader_name}内部个股分析 ---\n  无正流入个股数据"
    
    # 生成内部分析报告
    return generate_internal_analysis_report(df_internal, leader_name, sector_file, sector_total_amount)


def generate_internal_analysis_report(df_internal, sector_name, sector_file, sector_total_amount):
    """生成板块内部个股资金流分析报告（支持双龙头检测和占比判断）"""
    try:
        # 取前10名用于分析
        top_stocks = df_internal.head(10)
        
        if len(top_stocks) < 2:
            return f"  \n--- {sector_name}内部个股分析 ---\n  数据不足，仅有{len(top_stocks)}只正流入个股"
        
        # 计算内部断层 - 增强版（支持双龙头检测+占比判断）
        inflows = top_stocks['今日主力净流入-净额'].tolist()
        names = top_stocks['名称'].tolist()
        
        # 基础比例计算
        first_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else 1.0
        first_amount = inflows[0] / 1e8  # 第1名资金量（亿）
        second_amount = inflows[1] / 1e8  # 第2名资金量（亿）
        first_second_gap = (inflows[0] - inflows[1]) / 1e8  # 第1-2名差距（亿）
        
        # 【新增】占比判断逻辑
        first_ratio_sector = (first_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        second_ratio_sector = (second_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        dual_ratio_sector = first_ratio_sector + second_ratio_sector
        
        # 如果有第3名，计算第2-3名关系
        has_third = len(inflows) >= 3 and inflows[2] > 0
        if has_third:
            second_third_ratio = inflows[1] / inflows[2]
            third_amount = inflows[2] / 1e8
            second_third_gap = (inflows[1] - inflows[2]) / 1e8
            third_ratio_sector = (third_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        else:
            second_third_ratio = float('inf')
            third_amount = 0
            second_third_gap = 0
            third_ratio_sector = 0
        
        # 【优先级1】基于板块占比的绝对龙头判断
        is_absolute_leader = False
        absolute_leader_reason = ""
        
        # 更严格的绝对龙头标准
        if first_ratio_sector >= 40.0:  # 第1名占板块40%以上，绝对龙头
            is_absolute_leader = True
            absolute_leader_reason = f"板块占比极高({first_ratio_sector:.1f}%)，绝对龙头地位"
        elif first_ratio_sector >= 35.0 and first_ratio >= 1.25:  # 35%以上且明显优势
            is_absolute_leader = True
            absolute_leader_reason = f"板块占比很高({first_ratio_sector:.1f}%)且明显领先({first_ratio:.2f}倍)"
        
        # 【优先级2】基于数据分布的智能多龙头检测
        is_dual_leader = False
        is_triple_leader = False
        dual_leader_reason = ""
        triple_leader_reason = ""
        
        if not is_absolute_leader and has_third:
            # 动态分析数据分布特征
            top5_amounts = [x/1e8 for x in inflows[:min(5, len(inflows))] if x > 0]
            
            if len(top5_amounts) >= 3:
                import numpy as np
                
                # 计算分布特征
                amounts_array = np.array(top5_amounts)
                cv = np.std(amounts_array) / np.mean(amounts_array) if np.mean(amounts_array) > 0 else 0  # 变异系数
                
                # 计算梯度下降率
                gaps = [top5_amounts[i] / top5_amounts[i+1] for i in range(len(top5_amounts)-1)]
                first_gap = gaps[0] if len(gaps) > 0 else 1.0  # 第1-2名差距
                second_gap = gaps[1] if len(gaps) > 1 else 1.0  # 第2-3名差距
                third_gap = gaps[2] if len(gaps) > 2 else 1.0   # 第3-4名差距
                
                # 计算前三名占比
                third_ratio_sector = (top5_amounts[2] / sector_total_amount) * 100 if sector_total_amount > 0 else 0
                triple_ratio_sector = first_ratio_sector + second_ratio_sector + third_ratio_sector
                
                # 【优先检测】三足鼎立：前三名都很接近，且与第4名有差距
                triple_close = (first_gap < 1.35 and second_gap < 1.15)  # 前三名都很接近
                triple_dominance = triple_ratio_sector >= 50.0  # 前三名合计占比足够高
                third_significant = third_ratio_sector >= 12.0  # 第3名占比不能太小
                clear_separation_from_fourth = third_gap >= 1.20  # 与第4名有明显差距
                
                if (triple_close and triple_dominance and third_significant and 
                    (clear_separation_from_fourth or triple_ratio_sector >= 55.0)):
                    is_triple_leader = True
                    triple_leader_reason = (f"三足鼎立：前三名合计占比{triple_ratio_sector:.1f}%，"
                                          f"个别占比{first_ratio_sector:.1f}%、{second_ratio_sector:.1f}%、{third_ratio_sector:.1f}%，"
                                          f"内部接近(最大差距{max(first_gap, second_gap):.2f}倍)")
                
                # 【次要检测】双龙头：仅在非三足鼎立时检测
                elif not is_triple_leader:
                    # 双龙头检测条件：基于数据分布和相对差距
                    # 条件1：前两名合计占比足够高
                    dual_dominance = dual_ratio_sector >= 35.0
                    
                    # 条件2：个股占比平衡（避免一强一弱）
                    balanced_shares = (first_ratio_sector >= 15.0 and 
                                     second_ratio_sector >= 12.0 and
                                     first_ratio_sector < 35.0)
                    
                    # 条件3：前两名相对接近
                    close_leaders = first_gap < 1.40
                    
                    # 条件4：智能断层检测（基于分布特征）
                    # 如果数据分布较为均匀（CV较小），则要求更明显的断层
                    # 如果数据分布不均匀（CV较大），则可以接受较小的断层
                    if cv < 0.4:  # 分布相对均匀，要求明显断层
                        clear_separation = second_gap >= 1.20  # 提高要求到1.20
                    elif cv < 0.6:  # 分布中等，中等要求
                        clear_separation = second_gap >= 1.15  # 提高要求到1.15
                    else:  # 分布不均匀，降低要求
                        clear_separation = second_gap >= 1.10
                    
                    # 条件5：占比补偿机制（更严格）
                    # 如果前两名合计占比很高，可以适当放宽断层要求
                    if dual_ratio_sector >= 50.0:  # 提高到50%
                        ratio_compensation = second_gap >= 1.05
                    else:
                        ratio_compensation = False
                    
                    # 综合判断
                    if (dual_dominance and balanced_shares and close_leaders and 
                        (clear_separation or ratio_compensation)):
                        is_dual_leader = True
                        
                        if ratio_compensation and not clear_separation:
                            dual_leader_reason = (f"高占比双龙头：合计占比{dual_ratio_sector:.1f}%，"
                                                f"分布CV{cv:.2f}，第2-3名差距{second_gap:.2f}倍")
                        else:
                            dual_leader_reason = (f"标准双龙头：合计占比{dual_ratio_sector:.1f}%，"
                                                f"分布CV{cv:.2f}，明显领先第3名({second_gap:.2f}倍)")
        
        # 【优先级3】动态阈值版单龙头检测（原有逻辑，仅在无占比优势时使用）
        is_single_leader = False
        single_leader_reason = ""
        
        if not is_absolute_leader and not is_dual_leader:
            # 计算动态阈值 - 基于前10名数据
            top10_amounts = [x/1e8 for x in inflows[:min(10, len(inflows))] if x > 0]
            
            if len(top10_amounts) >= 3:
                # 策略1: 基于平均值的60%作为双龙头最低门槛
                avg_amount = sum(top10_amounts) / len(top10_amounts)
                min_leader_threshold = avg_amount * 0.6
                
                # 策略3: 动态绝对差距阈值（基于前10名标准差）
                import numpy as np
                std_amount = np.std(top10_amounts) if len(top10_amounts) > 1 else 0
                min_gap_threshold = max(0.1, std_amount * 0.3)  # 最小0.1亿
                
                # 单龙头条件（动态版）：
                # 条件1: 相对优势显著 (1.25倍以上)
                if first_ratio >= 1.25:
                    is_single_leader = True
                    single_leader_reason = f"相对优势显著({first_ratio:.2f}倍)"
                
                # 条件2: 绝对差距较大 (≥动态阈值) 且有一定相对优势 (≥1.1倍)
                elif first_second_gap >= min_gap_threshold and first_ratio >= 1.1:
                    is_single_leader = True
                    single_leader_reason = f"绝对差距较大({first_second_gap:.2f}亿)"
                
                # 条件3: 中等资金且有明显优势 (≥动态阈值 且 ≥1.15倍)
                elif first_amount >= min_leader_threshold and first_ratio >= 1.15:
                    is_single_leader = True
                    single_leader_reason = f"资金量优势({first_amount:.2f}亿，领先{first_second_gap:.2f}亿)"
            else:
                # 如果数据不足，降级为简单判断
                if first_ratio >= 1.2 and first_second_gap >= 0.1:
                    is_single_leader = True
                    single_leader_reason = f"数据有限情况下的单龙头判断({first_ratio:.2f}倍)"
        
        # 构建报告
        report_lines = [f"  \n--- {sector_name}板块内部个股资金流入 Top 10 ---"]
        report_lines.append(f"  板块总资金: {sector_total_amount:.2f}亿")
        
        if is_absolute_leader:
            report_lines.append(f"  【★★★ {sector_name}板块内部绝对龙头! ★★★】")
            report_lines.append(f"  内部格局: 绝对龙头")
            report_lines.append(f"  绝对龙头: 【{names[0]}】") 
            report_lines.append(f"  占比优势: {first_ratio_sector:.1f}% (个股资金{first_amount:.2f}亿)")
            report_lines.append(f"  相对优势: {first_ratio:.2f}倍，绝对差距{first_second_gap:.2f}亿")
            report_lines.append(f"  判断依据: {absolute_leader_reason}")
        elif is_triple_leader:
            report_lines.append(f"  【★★★ {sector_name}板块内部三足鼎立格局! ★★★】")
            report_lines.append(f"  内部格局: 三足鼎立")
            report_lines.append(f"  三强集团: 【{names[0]}】、【{names[1]}】、【{names[2]}】")
            report_lines.append(f"  占比分析: {first_ratio_sector:.1f}% + {second_ratio_sector:.1f}% + {third_ratio_sector:.1f}% = {triple_ratio_sector:.1f}%")
            report_lines.append(f"  资金分析: {first_amount:.2f}亿、{second_amount:.2f}亿、{third_amount:.2f}亿")
            report_lines.append(f"  内部差距: 第1-2名{first_ratio:.2f}倍，第2-3名{second_third_ratio:.2f}倍")
            report_lines.append(f"  判断依据: {triple_leader_reason}")
        elif is_dual_leader:
            report_lines.append(f"  【★★ {sector_name}板块内部发现双龙头格局! ★★】")
            report_lines.append(f"  内部格局: 双龙头领先")
            report_lines.append(f"  双龙头: 【{names[0]}】& 【{names[1]}】")
            report_lines.append(f"  占比分析: {first_ratio_sector:.1f}% + {second_ratio_sector:.1f}% = {dual_ratio_sector:.1f}%")
            report_lines.append(f"  资金分析: {first_amount:.2f}亿 vs {second_amount:.2f}亿 (相差{first_ratio:.2f}倍)")
            if has_third:
                report_lines.append(f"  领先优势: 前二名vs第3名 {second_third_ratio:.2f}倍，断层{second_third_gap:.2f}亿")
            report_lines.append(f"  判断依据: {dual_leader_reason}")
        elif is_single_leader:
            report_lines.append(f"  【★ {sector_name}板块内部发现个股龙头! ★】")
            report_lines.append(f"  内部格局: 单股龙头")
            report_lines.append(f"  个股龙头: 【{names[0]}】") 
            report_lines.append(f"  占比分析: {first_ratio_sector:.1f}% (个股资金{first_amount:.2f}亿)")
            report_lines.append(f"  领先优势: {first_ratio:.2f}倍，绝对差距{first_second_gap:.2f}亿")
            report_lines.append(f"  判断依据: {single_leader_reason}")
        else:
            report_lines.append(f"  【{sector_name}板块内部竞争激烈】")
            report_lines.append(f"  占比分析: 第1名{first_ratio_sector:.1f}%，第2名{second_ratio_sector:.1f}%")
            report_lines.append(f"  第1名优势: {first_ratio:.2f}倍，差距{first_second_gap:.2f}亿 (无明显龙头)")
        
        # 生成表格数据
        from tabulate import tabulate
        table_data = []
        for i, (_, row) in enumerate(top_stocks.iterrows(), 1):
            amount_yi = row['今日主力净流入-净额'] / 1e8
            table_data.append([i, row['名称'], f"{amount_yi:.2f}亿"])
        
        table_str = tabulate(table_data, headers=['排名', '股票名称', '主力净流入'], 
                           tablefmt='psql', showindex=False)
        report_lines.append(table_str)
        
        # 添加数据源信息
        report_lines.append(f"  数据源: {sector_file}")
        
        return "\n".join(report_lines)
        
    except Exception as e:
        return f"  \n--- {sector_name}内部个股分析 ---\n  生成报告失败: {e}"


def generate_smart_report(sector_type, market_state, max_gap, group_info, thresholds, total_score, scores, competition_info=None, current_time=None, data_dir=None):
    """智能报告生成器：生成详细的分析报告（增强版）"""
    
    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]
        
        return (f"【--- {sector_type}板块{competition_type}，无明显龙头 ---】\n"
                f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")
    
    # 判断是否发现断层
    is_gap_found = total_score >= thresholds["min_gap_score"]
    
    if not is_gap_found:
        # 未发现断层的报告
        leader_name = group_info["members"][0]
        return (f"【--- {sector_type}板块未发现显著资金断层 ---】\n"
                f"  当前龙头: 【{leader_name}】\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
    
    # 发现断层的详细报告
    if group_info["size"] == 1:
        gap_type = "单龙头断层"
        leader_desc = f"断层龙头: 【{group_info['members'][0]}】"
        leader_name = group_info['members'][0]
    else:
        gap_type = f"Top {group_info['size']} 领先集团"
        leader_desc = f"领先集团: 【{', '.join(group_info['members'])}】"
        leader_name = group_info['members'][0]  # 取第一个作为代表
    
    # 断层分析描述
    gap_desc = (f"在第{max_gap['position']+1}名后发现显著断层，"
                f"绝对差距{max_gap['abs_gap']/1e8:.2f}亿元，"
                f"相对差距{max_gap['rel_gap']:.2f}倍")
    
    # 市场状态描述
    market_desc = (f"{market_state['scale']}市场 (总计{market_state['total_top5']:.1f}亿)，"
                   f"集中度{market_state['concentration']:.1%}")
    
    report_lines = [
        f"【★★★★★ {sector_type}板块发现资金断层! ★★★★★】",
        f"  格局类型: {gap_type}",
        f"  {leader_desc}",
        f"  断层分析: {gap_desc}",
        f"  市场状态: {market_desc}",
        f"  综合得分: {total_score:.2f}分 (超过{thresholds['min_gap_score']:.1f}分阈值)"
    ]
    
    base_report = "\n".join(report_lines)
    
    # 【新增】断层龙头内部个股资金流分析
    if current_time is not None and data_dir is not None:
        internal_reports = []
        
        # 为每个领先集团成员生成内部分析
        for i, leader_name in enumerate(group_info['members']):
            # 获取对应的板块总资金量
            sector_total_amount = group_info["amounts"][i] if i < len(group_info["amounts"]) else group_info["amounts"][0]
            internal_report = analyze_sector_internal_flow(leader_name, current_time, data_dir, sector_type, sector_total_amount)
            internal_reports.append(internal_report)
        
        # 合并所有内部分析报告
        all_internal_reports = "\n".join(internal_reports)
        return base_report + "\n" + all_internal_reports
    else:
        return base_report


def analyze_funding_gap_v7(df_sectors, sector_type, current_time=None, data_dir=None):
    """V7智能断层检测：基于市场状态的动态多维度分析（完整版）"""
    
    # 1. 数据预处理
    positive_flow_df = df_sectors[df_sectors['今日主力净流入-净额'] > 0]
    if len(positive_flow_df) < MIN_SECTORS_FOR_ANALYSIS:
        return f"【--- {sector_type}板块数据不足，无法分析 ---】"

    inflows = positive_flow_df.head(ANALYSIS_TOP_N)['今日主力净流入-净额'].tolist()
    if len(inflows) < 2: 
        return f"【--- {sector_type}板块数据不足，无法分析 ---】"
    
    names = positive_flow_df.head(ANALYSIS_TOP_N)['名称'].tolist()
    
    # 2. 竞争激烈度分析 - 优先检查
    competition_info = analyze_market_competition(inflows, names)
    
    # 3. 市场状态感知
    market_state = analyze_market_state(inflows)
    
    # 4. 全局断层点搜索
    gap_scores, max_gap = find_all_gap_points(inflows)
    
    # 5. 集团识别
    group_info = identify_leading_group(inflows, max_gap["position"], names)
    
    # 6. 动态阈值计算
    thresholds = calculate_dynamic_thresholds(market_state)
    
    # 7. 综合评判
    total_score, scores = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)
    
    # 8. 生成智能报告（包含竞争检测和内部分析）
    return generate_smart_report(sector_type, market_state, max_gap, group_info, thresholds, total_score, scores, competition_info, current_time, data_dir)


# 保留原函数作为备份
def analyze_funding_gap_v6_backup(df_sectors, sector_type):
    positive_flow_df = df_sectors[df_sectors['今日主力净流入-净额'] > 0]
    if len(positive_flow_df) < MIN_SECTORS_FOR_ANALYSIS:
        return f"【--- {sector_type}板块数据不足，无法分析 ---】"

    inflows = positive_flow_df.head(ANALYSIS_TOP_N)['今日主力净流入-净额'].tolist()
    if len(inflows) < 2: return f"【--- {sector_type}板块数据不足，无法分析 ---】"

    top_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else float('inf')

    if top_ratio < GAP_MIN_LEAD_THRESHOLD:
        leader_name = positive_flow_df.iloc[0]['名称']
        return (f"【--- {sector_type}板块未发现显著资金断层 ---】\n"
                f"  当前龙头: 【{leader_name}】\n"
                f"  领先优势: {top_ratio:.2f}倍 (未达到最小门槛: {GAP_MIN_LEAD_THRESHOLD:.2f}倍)")

    context_ratios = []
    for i in range(1, len(inflows) - 1):
        if inflows[i + 1] > 0:
            context_ratios.append(inflows[i] / inflows[i + 1])

    max_context_ratio = max(context_ratios) if context_ratios else 1.0
    is_gap_found = top_ratio > max_context_ratio * GAP_CONTEXT_MULTIPLIER
    leader_name = positive_flow_df.iloc[0]['名称']

    report_lines = []
    if is_gap_found:
        report_lines.append(f"【★★★★★ {sector_type}板块发现资金断层! ★★★★★】")
        report_lines.append(f"  断层龙头: 【{leader_name}】")
        report_lines.append(
            f"  领先优势: {top_ratio:.2f}倍 (满足最小门槛，且显著大于背景最大差距 {max_context_ratio:.2f}倍)")
    else:
        report_lines.append(f"【--- {sector_type}板块未发现显著资金断层 ---】")
        report_lines.append(f"  当前龙头: 【{leader_name}】")
        report_lines.append(
            f"  领先优势: {top_ratio:.2f}倍 (虽超门槛，但未显著领先于背景最大差距 {max_context_ratio:.2f}倍)")

    return "\n".join(report_lines)


def analyze_acceleration_v6(df_current, df_previous):
    if df_previous.empty:
        return []

    # 使用suffixes确保列名不冲突
    merged_df = pd.merge(df_current, df_previous, on='名称', suffixes=('_curr', '_prev'))

    # 直接使用合并后的列名进行计算
    merged_df['delta'] = merged_df['今日主力净流入-净额_curr'] - merged_df['今日主力净流入-净额_prev']
    merged_df['ratio'] = merged_df['今日主力净流入-净额_curr'] / (merged_df['今日主力净流入-净额_prev'] + 1e-6)

    accelerating_sectors = merged_df[
        (merged_df['delta'] >= ACC_MIN_DELTA_THRESHOLD) &
        (merged_df['ratio'] >= ACC_MIN_RATIO_THRESHOLD) &
        (merged_df['今日主力净流入-净额_curr'] >= ACC_MIN_INFLOW_THRESHOLD)
        ]
    # 在返回结果中也加入type_curr以供显示
    return accelerating_sectors.to_dict('records')


def run_gap_analysis_backtest(date_str):
    """【V6 主函数】独立分析+加速度监控"""
    global previous_data_snapshot

    # 创建日志文件路径
    log_file_path = f"analysis_log_{date_str}.txt"

    # 使用日志捕获上下文管理器
    with LogCapture(log_file_path):
        print(f"--- 开始对日期 {date_str} 进行资金断层与加速度分析 (V6.1 - 错误修复版) ---")
        _run_analysis_core(date_str)
        print(f"\n分析日志已保存到: {log_file_path}")


def _run_analysis_core(date_str):
    """核心分析逻辑"""
    global previous_data_snapshot

    data_dir = os.path.join(BASE_DATA_DIR, date_str)
    if not os.path.isdir(data_dir):
        print(f"错误: 目录不存在 {data_dir}")
        return

    all_files = os.listdir(data_dir)
    # 修复文件匹配逻辑：支持多种文件名格式
    all_files = os.listdir(data_dir)
    
    # 使用新的分类函数筛选文件
    industry_files = []
    concept_files = []
    stock_flow_files = []
    
    for f in all_files:
        if f.endswith('.csv'):
            file_type = classify_file_type(f)
            if file_type == 'sector':
                industry_files.append(f)
            elif file_type == 'concept':
                concept_files.append(f)
            elif file_type == 'stock_flow':
                stock_flow_files.append(f)
    
    industry_files = sorted(industry_files)
    concept_files = sorted(concept_files)
    stock_flow_files = sorted(stock_flow_files)

    print(f"文件扫描结果：找到 {len(industry_files)} 个行业文件，{len(concept_files)} 个概念文件，{len(stock_flow_files)} 个个股资金流文件。")
    if not industry_files and not concept_files and not stock_flow_files: return

    # 修复时间戳提取逻辑：支持多种时间戳格式
    timestamps = set()
    for f in industry_files + concept_files + stock_flow_files:
        timestamp = extract_timestamp_from_filename(f)
        if timestamp:
            timestamps.add(timestamp)
    timestamps = sorted(list(timestamps))

    for ts_str in timestamps:
        current_sim_time = datetime.strptime(ts_str, '%H%M%S').time()

        if not (datetime.strptime("09:30", "%H:%M").time() <= current_sim_time <= datetime.strptime("11:30",
                                                                                                    "%H:%M").time() or \
                current_sim_time >= datetime.strptime("13:00", "%H:%M").time()):
            continue

        print("\n" + "=" * 25 + f" 模拟时间点: {current_sim_time} " + "=" * 25)

        latest_industry_file = find_latest_file(industry_files, current_sim_time)
        latest_concept_file = find_latest_file(concept_files, current_sim_time)
        all_sectors_list = []
        if latest_industry_file:
            try:
                df_ind = pd.read_csv(os.path.join(data_dir, latest_industry_file), encoding='utf-8-sig',
                                     on_bad_lines='skip')
                if '名称' in df_ind.columns and '今日主力净流入-净额' in df_ind.columns:
                    df_ind['type'] = '行业'
                    all_sectors_list.append(df_ind[['名称', '今日主力净流入-净额', 'type']])
            except Exception as e:
                print(f"读取行业文件失败: {e}")
        if latest_concept_file:
            try:
                df_con = pd.read_csv(os.path.join(data_dir, latest_concept_file), encoding='utf-8-sig',
                                     on_bad_lines='skip')
                
                # 支持多种概念文件格式的列名映射
                rename_needed = False
                rename_map = {}
                
                # 检查并映射名称列
                if '行业' in df_con.columns and '名称' not in df_con.columns:
                    rename_map['行业'] = '名称'
                    rename_needed = True
                
                # 检查并映射资金流入列
                if '净额' in df_con.columns and '今日主力净流入-净额' not in df_con.columns:
                    rename_map['净额'] = '今日主力净流入-净额'
                    rename_needed = True
                
                if rename_needed:
                    df_con.rename(columns=rename_map, inplace=True)
                
                if '名称' in df_con.columns and '今日主力净流入-净额' in df_con.columns:
                    # 检查数据单位：如果数值较小（<1000），可能是亿元单位，需要转换
                    sample_value = df_con['今日主力净流入-净额'].iloc[0] if len(df_con) > 0 else 0
                    
                    df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'].apply(convert_to_float)
                    
                    # 如果数值普遍较小，说明单位是亿元，需要转换为元
                    if sample_value != 0 and abs(sample_value) < 1000:
                        df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'] * 1e8
                    
                    df_con['type'] = '概念'
                    all_sectors_list.append(df_con[['名称', '今日主力净流入-净额', 'type']])
            except Exception as e:
                print(f"读取概念文件失败: {e}")

        if not all_sectors_list: continue

        all_sectors_df = pd.concat(all_sectors_list, ignore_index=True).drop_duplicates(subset=['名称'])
        all_sectors_df['名称'] = all_sectors_df['名称'].str.strip()
        all_sectors_df['今日主力净流入-净额'] = all_sectors_df['今日主力净流入-净额'].apply(convert_to_float)
        all_sectors_df.dropna(subset=['今日主力净流入-净额'], inplace=True)

        # ---【V6.1 核心修复】---

        # 准备上一分钟的数据
        if previous_data_snapshot:
            df_previous = pd.DataFrame.from_dict(previous_data_snapshot, orient='index')
            # 关键修复：将索引 '名称' 恢复为一列
            df_previous = df_previous.reset_index().rename(columns={'index': '名称'})
        else:
            df_previous = pd.DataFrame()

        # 进行加速度分析
        acceleration_report = analyze_acceleration_v6(all_sectors_df, df_previous)
        if acceleration_report:
            print("【▲▲▲▲▲ 资金加速度警报! ▲▲▲▲▲】")
            for item in acceleration_report:
                # 从合并后的数据中正确获取类型
                sector_type = item.get('type_curr', item.get('type', '未知'))
                print(f"  板块: 【{item['名称']}】({sector_type})")
                print(
                    f"  资金从 {format_amount(item['今日主力净流入-净额_prev'])} 猛增至 {format_amount(item['今日主力净流入-净额_curr'])} (增速: {item['ratio']:.2f}倍)")
        # --- 修复结束 ---

        # 0. 分析个股资金流
        latest_stock_flow_file = find_latest_file(stock_flow_files, current_sim_time)
        if latest_stock_flow_file:
            try:
                stock_flow_data = parse_stock_flow_data(os.path.join(data_dir, latest_stock_flow_file), 'stock_flow')
                if stock_flow_data is not None and len(stock_flow_data) > 0:
                    # 个股资金流断层分析
                    stock_flow_report = analyze_stock_flow_gap(stock_flow_data, current_sim_time, data_dir)
                    print(stock_flow_report)
                    
                    # 显示个股资金流Top 50
                    print("\n--- 个股资金流入 Top 50 ---")
                    # 过滤条件：主力净流入>0 且 涨跌幅>=0
                    positive_stocks = stock_flow_data[
                        (stock_flow_data['今日主力净流入-净额'] > 0) &
                        (stock_flow_data['今日涨跌幅'] >= 0)
                    ]
                    if not positive_stocks.empty:
                        display_stocks = positive_stocks.head(50).copy()

                        # 准备显示列
                        display_columns = ['排名', '名称']
                        headers = ['排名', '股票名称']

                        # 添加今日涨跌幅
                        if '今日涨跌幅' in display_stocks.columns:
                            display_stocks['涨跌幅'] = display_stocks['今日涨跌幅'].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else 'N/A')
                            display_columns.append('涨跌幅')
                            headers.append('今日涨跌幅')

                        # 主力净流入-净额
                        display_stocks['主力净流入-净额'] = display_stocks['今日主力净流入-净额'].apply(format_amount)
                        display_columns.append('主力净流入-净额')
                        headers.append('今日主力净流入-净额')

                        # 主力净流入-净占比
                        if '今日主力净流入-净占比' in display_stocks.columns:
                            display_stocks['主力净流入-净占比'] = display_stocks['今日主力净流入-净占比'].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else 'N/A')
                            display_columns.append('主力净流入-净占比')
                            headers.append('今日主力净流入-净占比')

                        # 添加排名
                        display_stocks['排名'] = range(1, len(display_stocks) + 1)

                        print(tabulate(display_stocks[display_columns], headers=headers,
                                     tablefmt='psql', showindex=False))
                    else:
                        print("无正流入个股数据。")
                else:
                    print("个股资金流数据解析失败或为空。")
            except Exception as e:
                print(f"个股资金流分析失败: {e}")
        else:
            print("未找到个股资金流文件。")

        # 1. 分析概念板块
        df_concepts = all_sectors_df[all_sectors_df['type'] == '概念'].sort_values(by='今日主力净流入-净额',
                                                                                   ascending=False)
        concept_report = analyze_funding_gap_v7(df_concepts, "概念", current_sim_time, data_dir)
        print(concept_report)
        
        # 显示概念板块Top 5（移到内部分析之后）
        print("\n--- 概念板块资金流入 Top 5 ---")
        if not df_concepts.empty:
            display_concepts = df_concepts.head(5).copy()
            display_concepts['主力净流入'] = display_concepts['今日主力净流入-净额'].apply(format_amount)
            display_concepts['排名'] = range(1, len(display_concepts) + 1)
            print(tabulate(display_concepts[['排名', '名称', '主力净流入']], headers=['排名', '概念名称', '主力净流入'],
                           tablefmt='psql', showindex=False))
        else:
            print("无概念板块数据。")

        # 2. 分析行业板块
        print("")
        df_industries = all_sectors_df[all_sectors_df['type'] == '行业'].sort_values(by='今日主力净流入-净额',
                                                                                     ascending=False)

        # 显示行业板块Top 5（移到内部分析之前）
        print("\n--- 行业板块资金流入 Top 5 ---")
        if not df_industries.empty:
            display_industries = df_industries.head(5).copy()
            display_industries['主力净流入'] = display_industries['今日主力净流入-净额'].apply(format_amount)
            display_industries['排名'] = range(1, len(display_industries) + 1)
            print(
                tabulate(display_industries[['排名', '名称', '主力净流入']], headers=['排名', '行业名称', '主力净流入'],
                         tablefmt='psql', showindex=False))
        else:
            print("无行业板块数据。")

        industry_report = analyze_funding_gap_v7(df_industries, "行业", current_sim_time, data_dir)
        print(industry_report)

        # 3. 循环结束前，更新“记忆”
        previous_data_snapshot = all_sectors_df.set_index('名称').to_dict('index')

    print("\n" + "=" * 70)
    print(f"日期 {date_str} 的资金断层与加速度分析全部完成。")
    print("=" * 70)


if __name__ == "__main__":
    run_gap_analysis_backtest(BACKTEST_DATE)