#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试同花顺备用接口功能
验证当东方财富接口失败时，是否能正确切换到同花顺接口
"""

import akshare as ak
import pandas as pd
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_ths_industry_interface():
    """测试同花顺行业资金流接口"""
    print("\n" + "="*50)
    print("测试同花顺行业资金流接口")
    print("="*50)
    
    try:
        # 测试同花顺行业资金流接口
        ths_industry_df = ak.stock_fund_flow_industry(symbol="即时")
        
        if ths_industry_df is not None and not ths_industry_df.empty:
            print(f"✅ 同花顺行业资金流接口成功，获取 {len(ths_industry_df)} 条数据")
            print(f"📊 数据列: {list(ths_industry_df.columns)}")
            print("\n前5条数据:")
            print(ths_industry_df.head())
            
            # 测试数据转换
            ths_std_df = ths_industry_df.copy()
            column_mapping = {
                '行业': '名称',
                '行业-涨跌幅': '今日涨跌幅', 
                '净额': '今日主力净流入-净额'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in ths_std_df.columns:
                    ths_std_df[new_col] = ths_std_df[old_col]
            
            # 转换单位
            if '今日主力净流入-净额' in ths_std_df.columns:
                ths_std_df['今日主力净流入-净额'] = ths_std_df['今日主力净流入-净额'] * 100000000
            
            print("\n转换后的数据格式:")
            required_cols = ['名称', '今日涨跌幅', '今日主力净流入-净额']
            if all(col in ths_std_df.columns for col in required_cols):
                print(ths_std_df[required_cols].head())
                print("✅ 数据格式转换成功")
            else:
                print(f"❌ 缺少必要列: {[col for col in required_cols if col not in ths_std_df.columns]}")
                
        else:
            print("❌ 同花顺行业资金流接口返回空数据")
            
    except Exception as e:
        print(f"❌ 同花顺行业资金流接口失败: {e}")

def test_ths_concept_interface():
    """测试同花顺概念资金流接口"""
    print("\n" + "="*50)
    print("测试同花顺概念资金流接口")
    print("="*50)
    
    try:
        # 测试同花顺概念资金流接口
        ths_concept_df = ak.stock_fund_flow_concept(symbol="即时")
        
        if ths_concept_df is not None and not ths_concept_df.empty:
            print(f"✅ 同花顺概念资金流接口成功，获取 {len(ths_concept_df)} 条数据")
            print(f"📊 数据列: {list(ths_concept_df.columns)}")
            print("\n前5条数据:")
            print(ths_concept_df.head())
            
            # 测试数据转换
            ths_std_df = ths_concept_df.copy()
            column_mapping = {
                '行业': '名称',
                '行业-涨跌幅': '今日涨跌幅', 
                '净额': '今日主力净流入-净额'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in ths_std_df.columns:
                    ths_std_df[new_col] = ths_std_df[old_col]
            
            # 转换单位
            if '今日主力净流入-净额' in ths_std_df.columns:
                ths_std_df['今日主力净流入-净额'] = ths_std_df['今日主力净流入-净额'] * 100000000
            
            print("\n转换后的数据格式:")
            required_cols = ['名称', '今日涨跌幅', '今日主力净流入-净额']
            if all(col in ths_std_df.columns for col in required_cols):
                print(ths_std_df[required_cols].head())
                print("✅ 数据格式转换成功")
            else:
                print(f"❌ 缺少必要列: {[col for col in required_cols if col not in ths_std_df.columns]}")
                
        else:
            print("❌ 同花顺概念资金流接口返回空数据")
            
    except Exception as e:
        print(f"❌ 同花顺概念资金流接口失败: {e}")

def test_ths_individual_interface():
    """测试同花顺个股资金流接口"""
    print("\n" + "="*50)
    print("测试同花顺个股资金流接口")
    print("="*50)
    
    try:
        # 测试同花顺个股资金流接口
        ths_stock_df = ak.stock_fund_flow_individual(symbol="即时")
        
        if ths_stock_df is not None and not ths_stock_df.empty:
            print(f"✅ 同花顺个股资金流接口成功，获取 {len(ths_stock_df)} 条数据")
            print(f"📊 数据列: {list(ths_stock_df.columns)}")
            print("\n前5条数据:")
            print(ths_stock_df.head())
            
            # 测试数据转换
            ths_std_df = ths_stock_df.copy()
            column_mapping = {
                '股票代码': '代码',
                '股票简称': '名称',
                '涨跌幅': '今日涨跌幅',
                '净额': '今日主力净流入-净额',
                '最新价': '最新价',
                '换手率': '换手率'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in ths_std_df.columns:
                    ths_std_df[new_col] = ths_std_df[old_col]
            
            # 处理代码格式
            if '代码' in ths_std_df.columns:
                ths_std_df['代码'] = ths_std_df['代码'].astype(str).str.zfill(6)
            
            print("\n转换后的数据格式:")
            display_cols = ['代码', '名称', '今日涨跌幅', '今日主力净流入-净额']
            available_cols = [col for col in display_cols if col in ths_std_df.columns]
            if available_cols:
                print(ths_std_df[available_cols].head())
                print("✅ 数据格式转换成功")
            else:
                print(f"❌ 缺少显示列: {display_cols}")
                
        else:
            print("❌ 同花顺个股资金流接口返回空数据")
            
    except Exception as e:
        print(f"❌ 同花顺个股资金流接口失败: {e}")

def test_eastmoney_interfaces():
    """测试东方财富接口（对比用）"""
    print("\n" + "="*50)
    print("测试东方财富接口（对比用）")
    print("="*50)
    
    # 测试东方财富行业资金流
    try:
        em_industry_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")
        if em_industry_df is not None and not em_industry_df.empty:
            print(f"✅ 东方财富行业资金流接口成功，获取 {len(em_industry_df)} 条数据")
        else:
            print("❌ 东方财富行业资金流接口返回空数据")
    except Exception as e:
        print(f"❌ 东方财富行业资金流接口失败: {e}")
    
    # 测试东方财富概念资金流
    try:
        em_concept_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="概念资金流")
        if em_concept_df is not None and not em_concept_df.empty:
            print(f"✅ 东方财富概念资金流接口成功，获取 {len(em_concept_df)} 条数据")
        else:
            print("❌ 东方财富概念资金流接口返回空数据")
    except Exception as e:
        print(f"❌ 东方财富概念资金流接口失败: {e}")
    
    # 测试东方财富个股资金流
    try:
        em_stock_df = ak.stock_individual_fund_flow_rank(indicator="今日")
        if em_stock_df is not None and not em_stock_df.empty:
            print(f"✅ 东方财富个股资金流接口成功，获取 {len(em_stock_df)} 条数据")
        else:
            print("❌ 东方财富个股资金流接口返回空数据")
    except Exception as e:
        print(f"❌ 东方财富个股资金流接口失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试同花顺备用接口功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 首先测试东方财富接口状态
    test_eastmoney_interfaces()
    
    # 测试同花顺备用接口
    test_ths_industry_interface()
    test_ths_concept_interface()
    test_ths_individual_interface()
    
    print("\n" + "="*50)
    print("🎉 测试完成")
    print("="*50)
    print("说明:")
    print("1. 如果东方财富接口正常，说明当前网络状况良好")
    print("2. 如果东方财富接口失败但同花顺接口成功，说明备用机制有效")
    print("3. 如果两个接口都失败，可能是网络问题或交易时间外")
    print("4. 数据格式转换测试确保备用接口数据能正确处理")
